<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>invalidUtf16Message | Truffle Decoding and Encoding</title><meta name="description" content="Documentation for Truffle Decoding and Encoding"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/search.js" id="tsd-search-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os"</script><header class="tsd-page-toolbar">
<div class="tsd-toolbar-contents container">
<div class="table-cell" id="tsd-search" data-base="..">
<div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M15.7824 13.833L12.6666 10.7177C12.5259 10.5771 12.3353 10.499 12.1353 10.499H11.6259C12.4884 9.39596 13.001 8.00859 13.001 6.49937C13.001 2.90909 10.0914 0 6.50048 0C2.90959 0 0 2.90909 0 6.49937C0 10.0896 2.90959 12.9987 6.50048 12.9987C8.00996 12.9987 9.39756 12.4863 10.5008 11.6239V12.1332C10.5008 12.3332 10.5789 12.5238 10.7195 12.6644L13.8354 15.7797C14.1292 16.0734 14.6042 16.0734 14.8948 15.7797L15.7793 14.8954C16.0731 14.6017 16.0731 14.1267 15.7824 13.833ZM6.50048 10.499C4.29094 10.499 2.50018 8.71165 2.50018 6.49937C2.50018 4.29021 4.28781 2.49976 6.50048 2.49976C8.71001 2.49976 10.5008 4.28708 10.5008 6.49937C10.5008 8.70852 8.71314 10.499 6.50048 10.499Z" fill="var(--color-text)"></path></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div>
<div class="field">
<div id="tsd-toolbar-links"></div></div>
<ul class="results">
<li class="state loading">Preparing search index...</li>
<li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Truffle Decoding and Encoding</a></div>
<div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><rect x="1" y="3" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="7" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="11" width="14" height="2" fill="var(--color-text)"></rect></svg></a></div></div></header>
<div class="container container-main">
<div class="col-content">
<div class="tsd-page-title">
<ul class="tsd-breadcrumb">
<li><a href="../index.html">Truffle Decoding and Encoding</a></li>
<li><a href="../modules/_truffle_codec.html">@truffle/codec</a></li>
<li><a href="../modules/_truffle_codec.Wrap.html">Wrap</a></li>
<li><a href="../modules/_truffle_codec.Wrap.Messages.html">Messages</a></li>
<li><a href="_truffle_codec.Wrap.Messages.invalidUtf16Message.html">invalidUtf16Message</a></li></ul>
<h1>Variable invalidUtf16Message<code class="tsd-tag ts-flagConst">Const</code> </h1></div>
<div class="tsd-signature"><span class="tsd-kind-variable">invalid<wbr/>Utf16<wbr/>Message</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> = &quot;Input string was not valid UTF-16&quot;</span></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/codec/lib/wrap/messages.ts#L25">codec/lib/wrap/messages.ts:25</a></li></ul></aside></div>
<div class="col-sidebar">
<div class="page-menu">
<div class="tsd-navigation settings">
<details class="tsd-index-accordion"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M4.93896 8.531L12 15.591L19.061 8.531L16.939 6.409L12 11.349L7.06098 6.409L4.93896 8.531Z" fill="var(--color-text)" id="icon-chevronDown"></path></svg>Settings</h3></summary>
<div class="tsd-accordion-details">
<div class="tsd-filter-visibility">
<h4 class="uppercase">Member Visibility</h4><form>
<ul id="tsd-filter-options">
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></form></div>
<div class="tsd-theme-toggle">
<h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div></div>
<div class="site-menu">
<nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-namespace)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.33 16V7.24H10.77L13.446 14.74C13.43 14.54 13.41 14.296 13.386 14.008C13.37 13.712 13.354 13.404 13.338 13.084C13.33 12.756 13.326 12.448 13.326 12.16V7.24H14.37V16H12.93L10.266 8.5C10.282 8.692 10.298 8.936 10.314 9.232C10.33 9.52 10.342 9.828 10.35 10.156C10.366 10.476 10.374 10.784 10.374 11.08V16H9.33Z" fill="var(--color-text)"></path></g></svg><span>Truffle <wbr/>Decoding and <wbr/>Encoding</span></a>
<ul class="tsd-small-nested-navigation">
<li>
<details class="tsd-index-accordion" open data-key="@truffle/codec"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="../modules/_truffle_codec.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/codec</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li><a href="../modules/_truffle_codec.AbiData.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Abi<wbr/>Data</span></a></li>
<li><a href="../modules/_truffle_codec.Ast.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Ast</span></a></li>
<li><a href="../modules/_truffle_codec.AstConstant.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Ast<wbr/>Constant</span></a></li>
<li><a href="../modules/_truffle_codec.Basic.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Basic</span></a></li>
<li><a href="../modules/_truffle_codec.Bytes.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Bytes</span></a></li>
<li><a href="../modules/_truffle_codec.Compilations.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Compilations</span></a></li>
<li><a href="../modules/_truffle_codec.Compiler.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Compiler</span></a></li>
<li><a href="../modules/_truffle_codec.Contexts.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Contexts</span></a></li>
<li><a href="../modules/_truffle_codec.Conversion.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Conversion</span></a></li>
<li><a href="../modules/_truffle_codec.Evm.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Evm</span></a></li>
<li><a href="../modules/_truffle_codec.Export.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Export</span></a></li>
<li><a href="../modules/_truffle_codec.Format.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Format</span></a></li>
<li><a href="../modules/_truffle_codec.MappingKey.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Mapping<wbr/>Key</span></a></li>
<li><a href="../modules/_truffle_codec.Memory.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Memory</span></a></li>
<li><a href="../modules/_truffle_codec.Pointer.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Pointer</span></a></li>
<li><a href="../modules/_truffle_codec.Special.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Special</span></a></li>
<li><a href="../modules/_truffle_codec.Stack.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Stack</span></a></li>
<li><a href="../modules/_truffle_codec.Storage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Storage</span></a></li>
<li><a href="../modules/_truffle_codec.Topic.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Topic</span></a></li>
<li>
<details class="tsd-index-accordion" open data-key="@truffle/codec.Wrap"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="../modules/_truffle_codec.Wrap.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Wrap</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li>
<details class="tsd-index-accordion" open data-key="@truffle/codec.Wrap.Messages"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="../modules/_truffle_codec.Wrap.Messages.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>Messages</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li><a href="_truffle_codec.Wrap.Messages.badEnumMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-32"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-variable)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.106 16L8.85 7.24H9.966L11.454 13.192C11.558 13.608 11.646 13.996 11.718 14.356C11.79 14.708 11.842 14.976 11.874 15.16C11.906 14.976 11.954 14.708 12.018 14.356C12.09 13.996 12.178 13.608 12.282 13.192L13.758 7.24H14.85L12.582 16H11.106Z" fill="var(--color-text)"></path></g></svg><span>bad<wbr/>Enum<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.checksumFailedMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>checksum<wbr/>Failed<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.errorResultMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>error<wbr/>Result<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.invalidUtf16Message.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>invalid<wbr/>Utf16<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.looseModeOnlyMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>loose<wbr/>Mode<wbr/>Only<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.negativeBytesMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>negative<wbr/>Bytes<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.nonIntegerMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>non<wbr/>Integer<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.nonNumericMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>non<wbr/>Numeric<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.nonSafeMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>non<wbr/>Safe<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.notAStringMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>notAString<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.outOfRangeEnumMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>out<wbr/>Of<wbr/>Range<wbr/>Enum<wbr/>Message</span></a></li>
<li><a href="_truffle_codec.Wrap.Messages.outOfRangeMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-32"></use></svg><span>out<wbr/>Of<wbr/>Range<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.notABytestringMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-64"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g></svg><span>notABytestring<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.overlongMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>overlong<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.specifiedTypeMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>specified<wbr/>Type<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.tooPreciseMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>too<wbr/>Precise<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.unrecognizedNumberMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>unrecognized<wbr/>Number<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.wrappedTypeMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrapped<wbr/>Type<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.wrongArrayLengthMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrong<wbr/>Array<wbr/>Length<wbr/>Message</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.Messages.wrongLengthMessage.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrong<wbr/>Length<wbr/>Message</span></a></li></ul></div></details></li>
<li><a href="../classes/_truffle_codec.Wrap.BadResponseTypeError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-128"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-class)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g></svg><span>Bad<wbr/>Response<wbr/>Type<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_codec.Wrap.NoOverloadsMatchedError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Overloads<wbr/>Matched<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_codec.Wrap.NoUniqueBestOverloadError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Unique<wbr/>Best<wbr/>Overload<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_codec.Wrap.TypeMismatchError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Type<wbr/>Mismatch<wbr/>Error</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.ContractInput.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-256"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-interface)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.51 16V15.016H11.298V8.224H9.51V7.24H14.19V8.224H12.402V15.016H14.19V16H9.51Z" fill="var(--color-text)"></path></g></svg><span>Contract<wbr/>Input</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.EncodingTextInput.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Encoding<wbr/>Text<wbr/>Input</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.FunctionExternalInput.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Function<wbr/>External<wbr/>Input</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.Method.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Method</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.Resolution.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Resolution</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.ResolveOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Resolve<wbr/>Options</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.TypeValueInput.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Type<wbr/>Value<wbr/>Input</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.Uint8ArrayLike.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Uint8<wbr/>Array<wbr/>Like</span></a></li>
<li><a href="../interfaces/_truffle_codec.Wrap.WrapOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Wrap<wbr/>Options</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.AddressLikeType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4194304"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g></svg><span>Address<wbr/>Like<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.AddressLikeValue.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Address<wbr/>Like<wbr/>Value</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.Case.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Case</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.DecimalType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Decimal<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.DecimalValue.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Decimal<wbr/>Value</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.IntegerOrEnumType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Integer<wbr/>Or<wbr/>Enum<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.IntegerOrEnumValue.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Integer<wbr/>Or<wbr/>Enum<wbr/>Value</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.IntegerType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Integer<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.IntegerValue.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Integer<wbr/>Value</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.NumericType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Numeric<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.TupleLikeType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Tuple<wbr/>Like<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.Wrap.TupleLikeValue.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Tuple<wbr/>Like<wbr/>Value</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.resolveAndWrap.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>resolve<wbr/>And<wbr/>Wrap</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.wrap.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrap</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.wrapForMethod.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrap<wbr/>For<wbr/>Method</span></a></li>
<li><a href="../functions/_truffle_codec.Wrap.wrapMultiple.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>wrap<wbr/>Multiple</span></a></li></ul></div></details></li>
<li><a href="../classes/_truffle_codec.NoProjectInfoError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Project<wbr/>Info<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_codec.RepeatCompilationIdError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Repeat<wbr/>Compilation<wbr/>Id<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_codec.UnknownUserDefinedTypeError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unknown<wbr/>User<wbr/>Defined<wbr/>Type<wbr/>Error</span></a></li>
<li><a href="../interfaces/_truffle_codec.AbiArgument.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Abi<wbr/>Argument</span></a></li>
<li><a href="../interfaces/_truffle_codec.AccessListForAddress.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Access<wbr/>List<wbr/>For<wbr/>Address</span></a></li>
<li><a href="../interfaces/_truffle_codec.AddressWrapRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Address<wbr/>Wrap<wbr/>Request</span></a></li>
<li><a href="../interfaces/_truffle_codec.AddressWrapResponse.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Address<wbr/>Wrap<wbr/>Response</span></a></li>
<li><a href="../interfaces/_truffle_codec.AnonymousDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Anonymous<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.BlockhashedMulticallInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Blockhashed<wbr/>Multicall<wbr/>Info</span></a></li>
<li><a href="../interfaces/_truffle_codec.BytecodeDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Bytecode<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.CallInterpretationInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Call<wbr/>Interpretation<wbr/>Info</span></a></li>
<li><a href="../interfaces/_truffle_codec.CodeRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Code<wbr/>Request</span></a></li>
<li><a href="../interfaces/_truffle_codec.ConstructorDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Constructor<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.DeadlinedMulticallInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Deadlined<wbr/>Multicall<wbr/>Info</span></a></li>
<li><a href="../interfaces/_truffle_codec.DecimalWrapRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decimal<wbr/>Wrap<wbr/>Request</span></a></li>
<li><a href="../interfaces/_truffle_codec.DecimalWrapResponse.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decimal<wbr/>Wrap<wbr/>Response</span></a></li>
<li><a href="../interfaces/_truffle_codec.EmptyFailureDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Empty<wbr/>Failure<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.EventDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Event<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.FunctionDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Function<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.IntegerWrapRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Integer<wbr/>Wrap<wbr/>Request</span></a></li>
<li><a href="../interfaces/_truffle_codec.IntegerWrapResponse.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Integer<wbr/>Wrap<wbr/>Response</span></a></li>
<li><a href="../interfaces/_truffle_codec.LogOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Log<wbr/>Options</span></a></li>
<li><a href="../interfaces/_truffle_codec.MessageDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Message<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.Options.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Options</span></a></li>
<li><a href="../interfaces/_truffle_codec.RawReturnDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Raw<wbr/>Return<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.ReturnDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Return<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.RevertMessageDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Revert<wbr/>Message<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.SelfDestructDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Self<wbr/>Destruct<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.StateVariable.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>State<wbr/>Variable</span></a></li>
<li><a href="../interfaces/_truffle_codec.StorageRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Storage<wbr/>Request</span></a></li>
<li><a href="../interfaces/_truffle_codec.TryAggregateInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Try<wbr/>Aggregate<wbr/>Info</span></a></li>
<li><a href="../interfaces/_truffle_codec.UnknownBytecodeDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Unknown<wbr/>Bytecode<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.UnknownCallDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Unknown<wbr/>Call<wbr/>Decoding</span></a></li>
<li><a href="../interfaces/_truffle_codec.UnknownCreationDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Unknown<wbr/>Creation<wbr/>Decoding</span></a></li>
<li><a href="../types/_truffle_codec.AccessList.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Access<wbr/>List</span></a></li>
<li><a href="../types/_truffle_codec.BlockSpecifier.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Block<wbr/>Specifier</span></a></li>
<li><a href="../types/_truffle_codec.CalldataDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Calldata<wbr/>Decoding</span></a></li>
<li><a href="../types/_truffle_codec.ContractKind.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Contract<wbr/>Kind</span></a></li>
<li><a href="../types/_truffle_codec.DecoderRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Decoder<wbr/>Request</span></a></li>
<li><a href="../types/_truffle_codec.DecodingMode.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Decoding<wbr/>Mode</span></a></li>
<li><a href="../types/_truffle_codec.ExtrasAllowed.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Extras<wbr/>Allowed</span></a></li>
<li><a href="../types/_truffle_codec.Location.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Location</span></a></li>
<li><a href="../types/_truffle_codec.LogDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Log<wbr/>Decoding</span></a></li>
<li><a href="../types/_truffle_codec.Mutability.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Mutability</span></a></li>
<li><a href="../types/_truffle_codec.PaddingMode.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Padding<wbr/>Mode</span></a></li>
<li><a href="../types/_truffle_codec.PaddingType.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Padding<wbr/>Type</span></a></li>
<li><a href="../types/_truffle_codec.ReturndataDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Returndata<wbr/>Decoding</span></a></li>
<li><a href="../types/_truffle_codec.Visibility.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Visibility</span></a></li>
<li><a href="../types/_truffle_codec.WrapRequest.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Wrap<wbr/>Request</span></a></li>
<li><a href="../types/_truffle_codec.WrapResponse.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Wrap<wbr/>Response</span></a></li>
<li><a href="../functions/_truffle_codec.abifyCalldataDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>abify<wbr/>Calldata<wbr/>Decoding</span></a></li>
<li><a href="../functions/_truffle_codec.abifyLogDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>abify<wbr/>Log<wbr/>Decoding</span></a></li>
<li><a href="../functions/_truffle_codec.abifyReturndataDecoding.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>abify<wbr/>Returndata<wbr/>Decoding</span></a></li>
<li><a href="../functions/_truffle_codec.decodeCalldata.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>decode<wbr/>Calldata</span></a></li>
<li><a href="../functions/_truffle_codec.decodeEvent.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>decode<wbr/>Event</span></a></li>
<li><a href="../functions/_truffle_codec.decodeReturndata.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>decode<wbr/>Returndata</span></a></li>
<li><a href="../functions/_truffle_codec.decodeRevert.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>decode<wbr/>Revert</span></a></li>
<li><a href="../functions/_truffle_codec.decodeVariable.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>decode<wbr/>Variable</span></a></li></ul></div></details></li>
<li><a href="../modules/_truffle_encoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/encoder</span></a></li>
<li><a href="../modules/decoder_lib.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>decoder/lib</span></a></li></ul></nav></div></div></div>
<div class="tsd-generator">
<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div>
<div class="overlay"></div></body></html>