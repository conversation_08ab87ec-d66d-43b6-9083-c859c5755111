{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../lib/basic/encode/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AACA,6DAAwD;AACxD,+CAA0C;AAE1C,kEAAkE;AAClE,2DAA2D;AAC3D,sEAAsE;AAEtE;;;;;GAKG;AACH,SAAgB,WAAW,CAAC,KAA0B;IACpD,IAAI,KAAiB,CAAC;IACtB,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;QAC5B,KAAK,sBAAsB;YACzB,OAAO,WAAW,CAA2C,KAAM,CAAC,KAAK,CAAC,CAAC;QAC7E,KAAK,MAAM,CAAC;QACZ,KAAK,KAAK;YACR,OAAO,UAAU,CAAC,OAAO,CAC4B,KAAM,CAAC,KAAK,CAAC,IAAI,EACpE,GAAG,CAAC,KAAK,CAAC,SAAS,CACpB,CAAC;QACJ,KAAK,MAAM;YACT,OAAO,UAAU,CAAC,OAAO,CACG,KAAM,CAAC,KAAK,CAAC,WAAW,EAClD,GAAG,CAAC,KAAK,CAAC,SAAS,CACpB,CAAC;QACJ,KAAK,MAAM,CAAC,CAAC;YACX,KAAK,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,0BAA0B;YACvE,IAA8B,KAAM,CAAC,KAAK,CAAC,SAAS,EAAE;gBACpD,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;aACpC;YACD,OAAO,KAAK,CAAC;SACd;QACD,KAAK,OAAO;YACV,QAAQ,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE;gBACvB,yCAAyC;gBACzC,KAAK,QAAQ;oBACX,KAAK,GAAG,UAAU,CAAC,OAAO,CACG,KAAM,CAAC,KAAK,CAAC,KAAK,CAC9C,CAAC;oBACF,IAAI,MAAM,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,uBAAuB;oBACzE,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;oBAClB,OAAO,MAAM,CAAC;aACjB;QACH,KAAK,SAAS;YACZ,OAAO,UAAU,CAAC,OAAO,CACM,KAAM,CAAC,KAAK,CAAC,SAAS,EACnD,GAAG,CAAC,KAAK,CAAC,SAAS,CACpB,CAAC;QACJ,KAAK,UAAU;YACb,OAAO,UAAU,CAAC,OAAO,CACO,KAAM,CAAC,KAAK,CAAC,OAAO,EAClD,GAAG,CAAC,KAAK,CAAC,SAAS,CACpB,CAAC;QACJ,KAAK,UAAU,CAAC,CAAC;YACf,QAAQ,KAAK,CAAC,IAAI,CAAC,UAAU,EAAE;gBAC7B,kEAAkE;gBAClE,8CAA8C;gBAC9C,KAAK,UAAU;oBACb,IAAI,YAAY,GAEf,KAAK,CAAC;oBACP,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,oBAAoB;oBACvE,IAAI,YAAY,GAAG,UAAU,CAAC,OAAO,CACnC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CACpC,CAAC,CAAC,kCAAkC;oBACrC,IAAI,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,YAAY,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,kCAAkC;oBACvG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;oBAC1B,OAAO,CAAC,GAAG,CAAC,aAAa,EAAE,GAAG,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,0BAA0B;oBAC9E,OAAO,OAAO,CAAC;aAClB;YACD,MAAM,CAAC,eAAe;SACvB;QACD,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,IAAI,QAAQ,GAA0D,CACpE,KAAK,CACL,CAAC,KAAK,CAAC,KAAK,CAAC;YACf,IAAI,YAAY,GAAG,UAAU,CAAC,UAAU,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YACtE,OAAO,UAAU,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;KAChE;AACH,CAAC;AAvED,kCAuEC"}