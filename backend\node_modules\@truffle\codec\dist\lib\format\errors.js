"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Contains the types for error and `ErrorResult` objects.
 * @category Main Format
 *
 * @packageDocumentation
 */
const debug_1 = __importDefault(require("debug"));
const debug = (0, debug_1.default)("codec:format:errors");
//# sourceMappingURL=errors.js.map