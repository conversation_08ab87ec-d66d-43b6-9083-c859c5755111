{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../lib/errors.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,iDAAgD;AAEhD,gFAAgF;AAChF,4EAA4E;AAC5E,6EAA6E;AAC7E;;GAEG;AACH,MAAa,aAAc,SAAQ,KAAK;IAEtC,YAAY,KAAqC;QAC/C,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;QAC7C,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AAPD,sCAOC;AAED,iEAAiE;AACjE,sCAAsC;AACtC,qEAAqE;AACrE,0EAA0E;AAC1E,OAAO;AACP;;GAEG;AACH,MAAa,iBAAkB,SAAQ,KAAK;IAG1C,iFAAiF;IACjF,wBAAwB;IACxB,YAAY,KAAiC,EAAE,UAAoB;QACjE,MAAM,OAAO,GAAG,sBAAsB,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,qCAAqC;QACzF,sFAAsF;QACtF,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,CAAC;IACxC,CAAC;CACF;AAZD,8CAYC;AAED;;GAEG;AACH,SAAgB,mBAAmB,CACjC,QAA2B,EAC3B,KAAU,EACV,SAAkB,KAAK;IAEvB,IAAI,KAAK,YAAY,aAAa,EAAE;QAClC,gBAAgB;QAChB,IAAI,MAAM,EAAE;YACV,wCAAwC;YACxC,MAAM,IAAI,iBAAiB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;SAC1C;aAAM;YACL,0CAA0C;YAC1C,OAAkC;gBAChC,wEAAwE;gBACxE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,OAAgB;gBACtB,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC;SACH;KACF;SAAM;QACL,uEAAuE;QACvE,MAAM,KAAK,CAAC;KACb;AACH,CAAC;AAvBD,kDAuBC;AAED;;;;GAIG;AACH,MAAa,kBAAmB,SAAQ,KAAK;IAC3C;QACE,KAAK,CAAC,mCAAmC,CAAC,CAAC;QAC3C,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AALD,gDAKC;AAED;;;GAGG;AACH,MAAa,wBAAyB,SAAQ,KAAK;IAEjD,YAAY,GAAa;QACvB,KAAK,CAAC,qBAAqB,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QACzE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,0BAA0B,CAAC;IACzC,CAAC;CACF;AAPD,4DAOC"}