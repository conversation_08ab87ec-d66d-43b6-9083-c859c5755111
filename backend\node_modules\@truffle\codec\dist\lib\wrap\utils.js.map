{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../lib/wrap/utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUA,oDAAyB;AACzB,0DAAwD;AACxD,iEAAyC,CAAC,iCAAiC;AAC3E,+DAAuC,CAAC,+BAA+B;AACvE,+DAAuC,CAAC,+BAA+B;AACvE,gDAAwB;AAExB,SAAgB,MAAM,CAAC,QAAqB;IAC1C,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,KAAK,KAAK,CAAC;QACX,KAAK,MAAM;YACT,OAAO,CAAC,CAAC;QACX,KAAK,OAAO,CAAC;QACb,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC,MAAM,CAAC;KAC1B;AACH,CAAC;AATD,wBASC;AAED,SAAgB,QAAQ,CAAC,QAAqB;IAC5C,IAAI,IAAI,GAAG,QAAQ,CAAC,IAAI,CAAC;IACzB,IAAI,QAAQ,CAAC,SAAS,KAAK,KAAK,IAAI,QAAQ,CAAC,SAAS,KAAK,OAAO,EAAE;QAClE,IAAI,IAAI,CAAC,CAAC,CAAC,uBAAuB;KACnC;IACD,MAAM,eAAe,GAAG,IAAI,gBAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;IACtD,OAAO,UAAU,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpE,CAAC;AAPD,4BAOC;AAED,SAAgB,QAAQ,CAAC,QAAqB;IAC5C,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM,IAAI,QAAQ,CAAC,SAAS,KAAK,QAAQ,EAAE;QACpE,OAAO,IAAI,gBAAG,CAAC,CAAC,CAAC,CAAC;KACnB;IACD,MAAM,eAAe,GAAG,IAAI,gBAAG,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,gBAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACxE,OAAO,UAAU,CAAC,YAAY,CAAC,eAAe,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;AACpE,CAAC;AAND,4BAMC;AAED,SAAgB,YAAY,CAAC,QAAqB,EAAE,KAAa;IAC/D,MAAM,QAAQ,GAAG,KAAK,GAAG,EAAE,IAAI,QAAQ,CAAC,MAAM,CAAC;IAC/C,OAAO,CACL,MAAM,CAAC,gBAAgB,IAAI,QAAQ,IAAI,QAAQ,IAAI,MAAM,CAAC,gBAAgB,CAC3E,CAAC;AACJ,CAAC;AALD,oCAKC;AAED,SAAgB,gBAAgB,CAAC,KAAU;IACzC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;QAC9B,OAAO,IAAI,KAAK;QAChB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAChC,CAAC;AACJ,CAAC;AARD,4CAQC;AAED,SAAgB,mBAAmB,CAAC,KAAU;IAC5C,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,OAAO,KAAK,CAAC,QAAQ,KAAK,QAAQ;QAClC,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;QAC9B,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,MAAM,KAAK,CAAC,CAChC,CAAC;AACJ,CAAC;AARD,kDAQC;AAED,SAAgB,eAAe,CAAC,KAAU;IACxC,OAAO,CACL,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC;QAC1D,KAAK,KAAK,IAAI;QACd,OAAO,KAAK,CAAC,OAAO,KAAK,QAAQ;QACjC,6DAA6D;QAC7D,6BAA6B;QAC7B,CAAC,CAAC,UAAU,IAAI,KAAK,CAAC,CACvB,CAAC;AACJ,CAAC;AATD,0CASC;AAED,SAAgB,uBAAuB,CACrC,KAAU;IAEV,OAAO,CACL,CAAC,OAAO,KAAK,KAAK,QAAQ,IAAI,OAAO,KAAK,KAAK,UAAU,CAAC;QAC1D,KAAK,KAAK,IAAI;QACd,SAAS,IAAI,KAAK;QAClB,UAAU,IAAI,KAAK,CACpB,CAAC;AACJ,CAAC;AATD,0DASC;AAED,SAAgB,eAAe,CAAC,KAAU;IACxC,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,KAAK,KAAK,IAAI;QACd,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ;QAC9B,KAAK,CAAC,IAAI,KAAK,IAAI;QACnB,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,QAAQ;QACxC,CAAC,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC;YAC1D,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,IAAI,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAC/D,CAAC;AACJ,CAAC;AAVD,0CAUC;AAED,SAAgB,gBAAgB,CAAC,KAAU;IACzC,OAAO,CACL,KAAK,YAAY,UAAU;QAC3B,CAAC,OAAO,KAAK,KAAK,QAAQ;YACxB,KAAK,KAAK,IAAI;YACd,OAAO,KAAK,CAAC,MAAM,KAAK,QAAQ,CAAC,CACpC,CAAC;AACJ,CAAC;AAPD,4CAOC;AAED,OAAO;AACP,SAAgB,aAAa,CAAC,KAAU;IACtC,OAAO,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,CAAC;AACrD,CAAC;AAFD,sCAEC;AAED,SAAgB,QAAQ,CAAC,KAAa;IACpC,MAAM,aAAa,GACjB,4DAA4D,CAAC,CAAC,yCAAyC;IACzG,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC,CAAC;AAC7C,CAAC;AAJD,4BAIC;AAED,SAAgB,YAAY,CAAC,MAAc;IACzC,MAAM,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,iCAAiC;IAClF,OAAO,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC;AACvD,CAAC;AAHD,oCAGC;AAED,SAAgB,WAAW,CAAC,KAAa;IACvC,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,qBAAqB,CAAC;IAC/C,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,CAAC;AAChD,CAAC;AAJD,kCAIC;AAED,SAAgB,qBAAqB,CAAC,KAAa;IACjD,MAAM,qBAAqB,GAAG,gBAAgB,CAAC;IAC/C,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC;AACrD,CAAC;AAHD,sDAGC;AAED,SAAgB,YAAY,CAAC,KAAa;IACxC,MAAM,iBAAiB,GAAG,0BAA0B,CAAC;IACrD,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAC;AACjD,CAAC;AAHD,oCAGC;AAED,SAAgB,2BAA2B,CAAC,KAAa;IACvD,MAAM,gCAAgC,GACpC,+CAA+C,CAAC;IAClD,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,gCAAgC,CAAC,CAAC,CAAC;AAChE,CAAC;AAJD,kEAIC;AAED,SAAgB,aAAa,CAAC,KAAU;IACtC,qDAAqD;IACrD,2DAA2D;IAC3D,0DAA0D;IAC1D,2CAA2C;IAC3C,OAAO,IAAA,kBAAQ,EAAC,KAAK,CAAC,IAAI,OAAyB,KAAM,KAAK,QAAQ,CAAC;AACzE,CAAC;AAND,sCAMC;AAED,SAAgB,aAAa,CAAC,KAAU;IACtC,8BAA8B;IAC9B,OAAO,IAAA,kBAAQ,EAAC,KAAK,CAAC,IAAI,OAAyB,KAAM,KAAK,QAAQ,CAAC;AACzE,CAAC;AAHD,sCAGC;AAED,SAAgB,cAAc,CAAC,KAAU;IACvC,8BAA8B;IAC9B,OAAO,IAAA,mBAAS,EAAC,KAAK,CAAC,IAAI,OAA2B,KAAM,KAAK,SAAS,CAAC;AAC7E,CAAC;AAHD,wCAGC;AAED,SAAgB,gBAAgB,CAC9B,KAAU;IAEV,OAAO,aAAa,CAAC,KAAK,CAAC,IAAI,aAAa,CAAC,KAAK,CAAC,IAAI,cAAc,CAAC,KAAK,CAAC,CAAC;AAC/E,CAAC;AAJD,4CAIC;AAED,SAAgB,YAAY,CAAC,KAAa;IACxC,IAAI;QACF,cAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,uBAAuB;QAC3C,OAAO,IAAI,CAAC;KACb;IAAC,WAAM;QACN,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AAPD,oCAOC;AAED,SAAgB,wBAAwB,CAAC,OAAe;IACtD,wCAAwC;IACxC,oDAAoD;IACpD,OAAO,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC;QACzB,CAAC,CAAC,oBAAoB,CAAC,OAAO,CAAC;QAC/B,CAAC,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;AACtC,CAAC;AAND,4DAMC;AAED,SAAgB,sBAAsB,CAAC,OAAe;IACpD,6EAA6E;IAC7E,kEAAkE;IAClE,aAAa;IACb,OAAO,0BAA0B,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;AACtD,CAAC;AALD,wDAKC;AAED,SAAgB,oBAAoB,CAAC,GAAW;IAC9C,cAAc;IACd,OAAO,0BAA0B,CAAC,GAAG,EAAE,kBAAkB,CAAC,CAAC;AAC7D,CAAC;AAHD,oDAGC;AAED,8EAA8E;AAC9E,yDAAyD;AACzD,SAAS,0BAA0B,CAAC,KAAa,EAAE,KAAa;IAC9D,IAAI,KAAK,CAAC;IACV,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE;QACnC,gEAAgE;QAChE,oDAAoD;QACpD,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC;KACxE;IACD,OAAO,KAAK,CAAC;AACf,CAAC"}