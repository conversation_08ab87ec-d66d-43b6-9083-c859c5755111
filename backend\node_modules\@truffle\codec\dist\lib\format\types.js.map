{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../../lib/format/types.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;;;;;AAEH,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,oBAAoB,CAAC,CAAC;AAsoBhD,SAAgB,kBAAkB,CAChC,kBAA2C;IAE3C,OAAO,MAAM,CAAC,MAAM,CAClB,EAAE,EACF,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,KAAK,CAAC,CAC/D,CAAC;AACJ,CAAC;AAPD,gDAOC;AAED,SAAS,iBAAiB,CAAC,OAAa;IACtC,MAAM,gBAAgB,GAAG;QACvB,UAAU;QACV,MAAM;QACN,QAAQ;QACR,sBAAsB;KACvB,CAAC;IACF,OAAO,gBAAgB,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;AACtD,CAAC;AAED,SAAgB,eAAe,CAAC,OAAa;IAC3C,MAAM,oBAAoB,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;IACtE,IAAI,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;QACpD,OAAO,IAAI,CAAC;KACb;SAAM,IAAI,OAAO,CAAC,SAAS,KAAK,OAAO,EAAE;QACxC,OAAO,OAAO,CAAC,IAAI,KAAK,SAAS,CAAC;KACnC;SAAM;QACL,OAAO,KAAK,CAAC;KACd;AACH,CAAC;AATD,0CASC;AAED,mFAAmF;AACnF,gEAAgE;AAChE,SAAgB,QAAQ,CAAC,SAAe,EAAE,gBAA2B;IACnE,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,EAAE;QACjC,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC;IACtB,IAAI,UAAU,GAAG,gBAAgB,CAAC,EAAE,CAAC,CAAC;IACtC,IAAI,CAAC,UAAU,EAAE;QACf,OAAO,SAAS,CAAC;KAClB;IACD,IAAI,UAAU,mCAAc,SAAS,GAAK,UAAU,CAAE,CAAC;IACvD,IAAI,eAAe,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,QAAQ,KAAK,SAAS,EAAE;QAClE,UAAU,GAAG,eAAe,CAAC,UAAU,EAAE,SAAS,CAAC,QAAQ,CAAC,CAAC;KAC9D;IACD,OAAO,UAAU,CAAC;AACpB,CAAC;AAdD,4BAcC;AAED,uFAAuF;AACvF,SAAgB,eAAe,CAC7B,QAAc,EACd,QAA8B;IAE9B,IAAI,eAAe,CAAC,QAAQ,CAAC,EAAE;QAC7B,QAAQ,QAAQ,CAAC,SAAS,EAAE;YAC1B,KAAK,QAAQ,CAAC;YACd,KAAK,OAAO;gBACV,uCAAY,QAAQ,KAAE,QAAQ,IAAG;YACnC,KAAK,OAAO;gBACV,uCACK,QAAQ,KACX,QAAQ,EACR,QAAQ,EAAE,eAAe,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,CAAC,IACtD;YACJ,KAAK,SAAS;gBACZ,IAAI,WAAW,GACb,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAE,SAAuB,CAAC,CAAC,CAAC,SAAS,CAAC;gBAChE,uCACK,QAAQ,KACX,QAAQ,EAAE,WAAW,EACrB,SAAS,EAAE,eAAe,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,IAC3D;YACJ,KAAK,QAAQ;gBACX,IAAI,UAAU,mCAAQ,QAAQ,KAAE,QAAQ,GAAE,CAAC;gBAC3C,IAAI,UAAU,CAAC,WAAW,EAAE;oBAC1B,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,CAAC,GAAG,CACjD,CAAC,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU,EAAE,EAAE,EAAE,CAAC,CAAC;wBAC3C,IAAI,EAAE,UAAU;wBAChB,IAAI,EAAE,eAAe,CAAC,UAAU,EAAE,QAAQ,CAAC;qBAC5C,CAAC,CACH,CAAC;iBACH;gBACD,OAAO,UAAU,CAAC;SACrB;KACF;SAAM;QACL,OAAO,QAAQ,CAAC;KACjB;AACH,CAAC;AAtCD,0CAsCC;AAED,wFAAwF;AACxF,sFAAsF;AACtF,uEAAuE;AACvE,sBAAsB;AAEtB,SAAgB,UAAU,CAAC,QAAc;IACvC,IAAI,UAAU,GAAG,yBAAyB,CAAC,QAAQ,CAAC,CAAC;IACrD,IAAI,eAAe,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,EAAE;QAClD,OAAO,UAAU,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;KAC7C;SAAM;QACL,OAAO,UAAU,CAAC;KACnB;AACH,CAAC;AAPD,gCAOC;AAED,SAAgB,yBAAyB,CAAC,QAAc;IACtD,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC,QAAQ,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;QACrD,KAAK,KAAK;YACR,OAAO,QAAQ,CAAC,QAAQ,IAAI,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpD,KAAK,MAAM;YACT,OAAO,QAAQ,CAAC,QAAQ,IAAI,MAAM,CAAC;QACrC,KAAK,OAAO;YACV,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBACrB,OAAO,QAAQ,CAAC,QAAQ,CAAC;aAC1B;YACD,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,SAAS;oBACZ,OAAO,OAAO,CAAC;gBACjB,KAAK,QAAQ;oBACX,OAAO,QAAQ,QAAQ,CAAC,MAAM,EAAE,CAAC;aACpC;QACH,KAAK,SAAS;YACZ,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,SAAS;oBACZ,OAAO,QAAQ,CAAC,QAAQ,IAAI,SAAS,CAAC,CAAC,UAAU;gBACnD,KAAK,UAAU;oBACb,OAAO,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,SAAS,CAAC;aAC3D;QACH,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC;QACvC,KAAK,OAAO;YACV,OAAO,QAAQ,CAAC,QAAQ,IAAI,QAAQ,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;QACzE,KAAK,QAAQ;YACX,OAAO,QAAQ,CAAC,QAAQ,IAAI,SAAS,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,MAAM,EAAE,CAAC;QAC1E,KAAK,OAAO;YACV,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBACrB,OAAO,QAAQ,CAAC,QAAQ,CAAC;aAC1B;YACD,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,SAAS;oBACZ,OAAO,GAAG,yBAAyB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC7D,KAAK,QAAQ;oBACX,OAAO,GAAG,yBAAyB,CAAC,QAAQ,CAAC,QAAQ,CAAC,IACpD,QAAQ,CAAC,MACX,GAAG,CAAC;aACP;QACH,KAAK,SAAS;YACZ,OAAO,WAAW,yBAAyB,CACzC,QAAQ,CAAC,OAAO,CACjB,OAAO,yBAAyB,CAAC,QAAQ,CAAC,SAAS,CAAC,GAAG,CAAC;QAC3D,KAAK,QAAQ,CAAC;QACd,KAAK,MAAM;YACT,sCAAsC;YACtC,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,OAAO;oBACV,OAAO,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,oBAAoB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACvF,KAAK,QAAQ;oBACX,OAAO,GAAG,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;aACvD;YACD,MAAM,CAAC,kBAAkB;QAC3B,KAAK,sBAAsB;YACzB,yDAAyD;YACzD,QAAQ,QAAQ,CAAC,IAAI,EAAE;gBACrB,KAAK,OAAO;oBACV,OAAO,GAAG,QAAQ,CAAC,oBAAoB,IAAI,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBACjE,KAAK,QAAQ;oBACX,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;aACjC;YACD,MAAM,CAAC,kBAAkB;QAC3B,KAAK,OAAO;YACV,OAAO,CACL,QAAQ,CAAC,QAAQ;gBACjB,QAAQ;oBACN,QAAQ,CAAC,WAAW;yBACjB,GAAG,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;yBAC9C,IAAI,CAAC,GAAG,CAAC;oBACZ,GAAG,CACN,CAAC,CAAC,wDAAwD;QAC7D,KAAK,UAAU;YACb,OAAO,QAAQ,CAAC,YAAY,GAAG,GAAG,GAAG,QAAQ,CAAC,QAAQ,CAAC;QACzD,KAAK,OAAO;YACV,6BAA6B;YAC7B,MAAM,aAAa,GAAG;gBACpB,OAAO,EAAE,KAAK;gBACd,WAAW,EAAE,IAAI;gBACjB,KAAK,EAAE,OAAO;aACf,CAAC;YACF,OAAO,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1C,KAAK,MAAM;YACT,OAAO,QAAQ,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;QAC9C,KAAK,UAAU;YACb,IAAI,gBAAwB,CAAC;YAC7B,QAAQ,QAAQ,CAAC,UAAU,EAAE;gBAC3B,KAAK,UAAU;oBACb,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;wBAC/B,IAAI,QAAQ,CAAC,QAAQ,EAAE;4BACrB,OAAO,QAAQ,CAAC,QAAQ,CAAC;yBAC1B;6BAAM;4BACL,OAAO,mBAAmB,CAAC,CAAC,YAAY;yBACzC;qBACF;oBACD,gBAAgB,GAAG,WAAW,CAAC,CAAC,4BAA4B;oBAC5D,MAAM;gBACR,KAAK,UAAU;oBACb,gBAAgB,GAAG,EAAE,CAAC;oBACtB,MAAM;aACT;YACD,IAAI,gBAAgB,GAClB,QAAQ,CAAC,UAAU,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,kCAAkC;YAC3G,IAAI,SAAS,GAAG,QAAQ,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,yDAAyD;YACjI,IAAI,UAAU,GAAG,QAAQ,CAAC,oBAAoB,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACzE,IAAI,WAAW,GAAG,YAAY,SAAS,GAAG,CAAC;YAC3C,IAAI,YAAY,GAAG,UAAU,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,aAAa,UAAU,GAAG,CAAC,CAAC,kCAAkC;YAC1G,OAAO,WAAW,GAAG,gBAAgB,GAAG,gBAAgB,GAAG,YAAY,CAAC;QAC1E,KAAK,SAAS;YACZ,2DAA2D;YAC3D,OAAO,SAAS,CAAC;KACpB;AACH,CAAC;AAnHD,8DAmHC;AAED,SAAgB,qBAAqB,CACnC,OAAa;IAEb,MAAM,oBAAoB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,sBAAsB,CAAC,CAAC;IACxE,OAAO,CACL,oBAAoB,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC;QACG,OAAQ,CAAC,IAAI,KAAK,OAAO,CAC7E,CAAC;AACJ,CAAC;AARD,sDAQC"}