<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>decoder/lib | Truffle Decoding and Encoding</title><meta name="description" content="Documentation for Truffle Decoding and Encoding"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/search.js" id="tsd-search-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os"</script><header class="tsd-page-toolbar">
<div class="tsd-toolbar-contents container">
<div class="table-cell" id="tsd-search" data-base="..">
<div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M15.7824 13.833L12.6666 10.7177C12.5259 10.5771 12.3353 10.499 12.1353 10.499H11.6259C12.4884 9.39596 13.001 8.00859 13.001 6.49937C13.001 2.90909 10.0914 0 6.50048 0C2.90959 0 0 2.90909 0 6.49937C0 10.0896 2.90959 12.9987 6.50048 12.9987C8.00996 12.9987 9.39756 12.4863 10.5008 11.6239V12.1332C10.5008 12.3332 10.5789 12.5238 10.7195 12.6644L13.8354 15.7797C14.1292 16.0734 14.6042 16.0734 14.8948 15.7797L15.7793 14.8954C16.0731 14.6017 16.0731 14.1267 15.7824 13.833ZM6.50048 10.499C4.29094 10.499 2.50018 8.71165 2.50018 6.49937C2.50018 4.29021 4.28781 2.49976 6.50048 2.49976C8.71001 2.49976 10.5008 4.28708 10.5008 6.49937C10.5008 8.70852 8.71314 10.499 6.50048 10.499Z" fill="var(--color-text)"></path></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div>
<div class="field">
<div id="tsd-toolbar-links"></div></div>
<ul class="results">
<li class="state loading">Preparing search index...</li>
<li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Truffle Decoding and Encoding</a></div>
<div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><rect x="1" y="3" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="7" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="11" width="14" height="2" fill="var(--color-text)"></rect></svg></a></div></div></header>
<div class="container container-main">
<div class="col-content">
<div class="tsd-page-title">
<ul class="tsd-breadcrumb">
<li><a href="../index.html">Truffle Decoding and Encoding</a></li>
<li><a href="decoder_lib.html">decoder/lib</a></li></ul>
<h1>Module decoder/lib</h1></div>
<section class="tsd-panel tsd-comment">
<div class="tsd-comment tsd-typography"><a id="md:truffle-decoder" class="tsd-anchor"></a><h1><a href="#md:truffle-decoder">Truffle Decoder</a></h1><p>This module provides an interface for decoding contract state, transaction
calldata, events, and return values and revert strings.  It&#39;s an interface to
the same low-level decoding functionality that Truffle Debugger uses.  However,
it has additional functionality that the debugger does not need, and the
debugger has additional functionality that this decoder does not need.</p>
<p>The interface is split into three classes: The project decoder, the contract
decoder, and the contract instance decoder.  The project decoder is associated
to the project as a whole and decodes transaction calldata and events.  The
contract decoder is associated to a specific contract class.  It has all the
capabilities of the project decoder, but it can also decode return values from
calls made by the given contract class.  The contract instance decoder is
associated to a specific contract instance; it again has all the capabilities
of the project decoder and contract decoder, but it can also decode the state
variables for the specific instance.  (In addition, in the case that the
contract does not include a <code>deployedBytecode</code> field in its artifact, which can
hinder decoding certain things, the contract instance decoder can sometimes
work around this where the other decoders cannot.)</p>
<p>This documentation describes the current state of the decoder, but further
improvements are planned.</p>
<a id="md:usage" class="tsd-anchor"></a><h2><a href="#md:usage">Usage</a></h2><a id="md:initialization" class="tsd-anchor"></a><h3><a href="#md:initialization">Initialization</a></h3><p>Create a decoder with one of the various constructor functions.</p>
<p>For a project decoder, use the [[forProject|<code>forProject</code>]] function.</p>
<p>For a contract decoder, use the [[forArtifact|<code>forArtifact</code>]] or
[[forContract|<code>forContract</code>]] function.</p>
<p>For a contract instance decoder, use one of the following:</p>
<ul>
<li>[[forDeployedArtifact|<code>forDeployedArtifact</code>]]</li>
<li>[[forDeployedContract|<code>forDeployedContract</code>]]</li>
<li>[[forArtifactAt|<code>forArtifactAt</code>]]</li>
<li>[[forContractAt|<code>forContractAt</code>]]</li>
<li>[[forContractInstance|<code>forContractInstance</code>]]</li>
<li>[[forAddress|<code>forAddress</code>]]</li>
</ul>
<p>See the documentation of these functions for details, or below for usage
examples.</p>
<p>All of these functions take a final argument in which information about the
project is specified; currently only a few methods for specifying project
information are allowed, but more are planned.</p>
<p>One can also spawn decoders from other decoders by supplying additional
information.  See the documentation for the individual decoder classes for a
method listing.</p>
<a id="md:decoder-methods" class="tsd-anchor"></a><h3><a href="#md:decoder-methods">Decoder methods</a></h3><p>See the documentation for the individual decoder classes for a method listing.</p>
<a id="md:output-format-information" class="tsd-anchor"></a><h3><a href="#md:output-format-information">Output format information</a></h3><p>The decoder outputs lossless, machine-readable [[Format.Values.Result]] objects
containing individual decoded values. See the [[Format|format documentation]]
for an overview and complete module listing.</p>
<a id="md:decoding-modes-abification-and-caveats" class="tsd-anchor"></a><h3><a href="#md:decoding-modes-abification-and-caveats">Decoding modes, abification, and caveats</a></h3><p>The decoder runs in either of two modes: full mode or ABI mode. Full mode
requires some additional constraints but returns substantially more detailed
information. Please see the notes on <a href="../#decoding-modes">decoding modes</a> for
more about this distinction.</p>
<p>See also the notes about <a href="../#additional-notes-on-decoding-state-variables">decoding state variables</a> for additional
caveats about what may or may not be fully decodable.</p>
<a id="md:basic-usage-examples" class="tsd-anchor"></a><h3><a href="#md:basic-usage-examples">Basic usage examples</a></h3><a id="md:decoding-a-log-with-the-project-decoder" class="tsd-anchor"></a><h4><a href="#md:decoding-a-log-with-the-project-decoder">Decoding a log with the project decoder</a></h4><p>This usage example is for a project with two contracts, <code>Contract1</code> and
<code>Contract2</code>.</p>
<pre><code class="language-typescript"><span class="hl-1">import</span><span class="hl-2"> { </span><span class="hl-4">forProject</span><span class="hl-2"> } </span><span class="hl-1">from</span><span class="hl-2"> </span><span class="hl-5">&quot;@truffle/decoder&quot;</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract1</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract1&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract2</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract2&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">provider</span><span class="hl-2"> = </span><span class="hl-4">web3</span><span class="hl-2">.</span><span class="hl-4">currentProvider</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">decoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">Decoder</span><span class="hl-2">.</span><span class="hl-6">forProject</span><span class="hl-2">(</span><span class="hl-4">provider</span><span class="hl-2">, [</span><span class="hl-4">contract1</span><span class="hl-2">, </span><span class="hl-4">contract2</span><span class="hl-2">]);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">decodings</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">decoder</span><span class="hl-2">.</span><span class="hl-6">decodeLog</span><span class="hl-2">(</span><span class="hl-4">log</span><span class="hl-2">);</span>
</code><button>Copy</button></pre>
<p>The usage of [[ProjectDecoder.decodeTransaction|decodeTransaction]] is similar.</p>
<p>For getting already-decoded logs meeting appropriate conditions, see
[[ProjectDecoder.events]].</p>
<a id="md:decoding-state-variables-with-the-contract-instance-decoder" class="tsd-anchor"></a><h4><a href="#md:decoding-state-variables-with-the-contract-instance-decoder">Decoding state variables with the contract instance decoder</a></h4><p>This usage example is for decoding the state variables of a contract <code>Contract</code>
in a project that also contains a contract <code>OtherContract</code>.</p>
<pre><code class="language-typescript"><span class="hl-1">import</span><span class="hl-2"> { </span><span class="hl-4">forContract</span><span class="hl-2"> } </span><span class="hl-1">from</span><span class="hl-2"> </span><span class="hl-5">&quot;@truffle/decoder&quot;</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">otherContract</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;OtherContract&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">decoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">Decoder</span><span class="hl-2">.</span><span class="hl-6">forContract</span><span class="hl-2">(</span><span class="hl-4">contract</span><span class="hl-2">, [</span><span class="hl-4">otherContract</span><span class="hl-2">]);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">instanceDecoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">decoder</span><span class="hl-2">.</span><span class="hl-6">forInstance</span><span class="hl-2">();</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">variables</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">instanceDecoder</span><span class="hl-2">.</span><span class="hl-6">variables</span><span class="hl-2">();</span>
</code><button>Copy</button></pre>
<p>In this example, we use the deployed version of <code>Contract</code>.  If we wanted an
instance at a different address, we could pass the address to <code>forInstance</code>.</p>
<p>In addition, rather than using <code>forContract</code> and then <code>forInstance</code>, we could
also use [[forDeployedContract|<code>forContractInstance</code>]] to perform both of these
in one step.  If we wanted to do this with a specified address, we could use
[[forContractAt|<code>forContractAt</code>]].</p>
<p>Yet another way would be:</p>
<pre><code class="language-typescript"><span class="hl-1">import</span><span class="hl-2"> { </span><span class="hl-4">forContractInstance</span><span class="hl-2"> } </span><span class="hl-1">from</span><span class="hl-2"> </span><span class="hl-5">&quot;@truffle/decoder&quot;</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">otherContract</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;OtherContract&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">deployedContract</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">contract</span><span class="hl-2">.</span><span class="hl-6">deployed</span><span class="hl-2">();</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">instanceDecoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">Decoder</span><span class="hl-2">.</span><span class="hl-6">forContractInstance</span><span class="hl-2">(</span><span class="hl-4">deployedContract</span><span class="hl-2">, [</span><span class="hl-4">otherContract</span><span class="hl-2">]);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">variables</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">instanceDecoder</span><span class="hl-2">.</span><span class="hl-6">variables</span><span class="hl-2">();</span>
</code><button>Copy</button></pre>
<p>These examples are not exhaustive.</p>
<p>One can find more advanced decoding examples with
[[ContractInstanceDecoder.variable|<code>variable</code>]] and
[[ContractInstanceDecoder.watchMappingKey|<code>watchMappingKey</code>]] at the
documentation for these individual functions.
 *
 *</p>
</div>
<div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/index.ts#L1">decoder/lib/index.ts:1</a></li></ul></aside>
<section class="tsd-panel-group tsd-index-group">
<section class="tsd-panel tsd-index-panel">
<details class="tsd-index-content tsd-index-accordion" open><summary class="tsd-accordion-summary tsd-index-summary">
<h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex=0><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M1.5 5.50969L8 11.6609L14.5 5.50969L12.5466 3.66086L8 7.96494L3.45341 3.66086L1.5 5.50969Z" fill="var(--color-text)"></path></svg> Index</h5></summary>
<div class="tsd-accordion-details">
<section class="tsd-index-section">
<h3 class="tsd-index-heading">References</h3>
<div class="tsd-index-list"><a href="decoder_lib.html#ExtrasAllowed" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-8388608"><rect fill="var(--color-icon-background)" stroke="#FF4D82" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M10.354 17V8.24H13.066C13.586 8.24 14.042 8.348 14.434 8.564C14.826 8.772 15.13 9.064 15.346 9.44C15.562 9.816 15.67 10.256 15.67 10.76C15.67 11.352 15.514 11.86 15.202 12.284C14.898 12.708 14.482 13 13.954 13.16L15.79 17H14.518L12.838 13.28H11.434V17H10.354ZM11.434 12.308H13.066C13.514 12.308 13.874 12.168 14.146 11.888C14.418 11.6 14.554 11.224 14.554 10.76C14.554 10.288 14.418 9.912 14.146 9.632C13.874 9.352 13.514 9.212 13.066 9.212H11.434V12.308Z" fill="var(--color-text)"></path></g></svg><span>Extras<wbr/>Allowed</span></a>
<a href="decoder_lib.html#StateVariable" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>State<wbr/>Variable</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Classes - Decoder</h3>
<div class="tsd-index-list"><a href="../classes/decoder_lib.ContractDecoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-128"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-class)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g></svg><span>Contract<wbr/>Decoder</span></a>
<a href="../classes/decoder_lib.ContractInstanceDecoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Instance<wbr/>Decoder</span></a>
<a href="../classes/decoder_lib.ProjectDecoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Project<wbr/>Decoder</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Classes - Exception</h3>
<div class="tsd-index-list"><a href="../classes/decoder_lib.ContractAllocationFailedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Allocation<wbr/>Failed<wbr/>Error</span></a>
<a href="../classes/decoder_lib.ContractBeingDecodedHasNoNodeError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Being<wbr/>Decoded<wbr/>Has<wbr/>No<wbr/>Node<wbr/>Error</span></a>
<a href="../classes/decoder_lib.ContractNotFoundError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Found<wbr/>Error</span></a>
<a href="../classes/decoder_lib.InvalidAddressError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Address<wbr/>Error</span></a>
<a href="../classes/decoder_lib.VariableNotFoundError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Variable<wbr/>Not<wbr/>Found<wbr/>Error</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Classes - Other</h3>
<div class="tsd-index-list"><a href="../classes/decoder_lib.NoProviderError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Provider<wbr/>Error</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Interfaces - Inputs</h3>
<div class="tsd-index-list"><a href="../interfaces/decoder_lib.DecodeLogOptions.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-256"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-interface)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.51 16V15.016H11.298V8.224H9.51V7.24H14.19V8.224H12.402V15.016H14.19V16H9.51Z" fill="var(--color-text)"></path></g></svg><span>Decode<wbr/>Log<wbr/>Options</span></a>
<a href="../interfaces/decoder_lib.EventOptions.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Event<wbr/>Options</span></a>
<a href="../interfaces/decoder_lib.Log.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Log</span></a>
<a href="../interfaces/decoder_lib.ReturnOptions.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Return<wbr/>Options</span></a>
<a href="../interfaces/decoder_lib.Transaction.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Transaction</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Interfaces - Results</h3>
<div class="tsd-index-list"><a href="../interfaces/decoder_lib.ContractState.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Contract<wbr/>State</span></a>
<a href="../interfaces/decoder_lib.DecodedLog.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoded<wbr/>Log</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Interfaces - Other</h3>
<div class="tsd-index-list"><a href="../interfaces/decoder_lib.DecoderSettings.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoder<wbr/>Settings</span></a>
<a href="../interfaces/decoder_lib.EnsSettings.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Ens<wbr/>Settings</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Type Aliases</h3>
<div class="tsd-index-list"><a href="../types/decoder_lib.ProjectInfo.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4194304"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g></svg><span>Project<wbr/>Info</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Functions - Provider-based Constructor</h3>
<div class="tsd-index-list"><a href="../functions/decoder_lib.forAddress.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-64"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g></svg><span>for<wbr/>Address</span></a>
<a href="../functions/decoder_lib.forArtifact.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact</span></a>
<a href="../functions/decoder_lib.forArtifactAt.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact<wbr/>At</span></a>
<a href="../functions/decoder_lib.forDeployedArtifact.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Artifact</span></a>
<a href="../functions/decoder_lib.forProject.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Functions - Truffle Contract-based Constructor</h3>
<div class="tsd-index-list"><a href="../functions/decoder_lib.forContract.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract</span></a>
<a href="../functions/decoder_lib.forContractAt.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>At</span></a>
<a href="../functions/decoder_lib.forContractInstance.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>Instance</span></a>
<a href="../functions/decoder_lib.forDeployedContract.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Contract</span></a>
</div></section></div></details></section></section>
<section class="tsd-panel-group tsd-member-group">
<h2>References</h2>
<section class="tsd-panel tsd-member"><a id="ExtrasAllowed" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><span>Extras<wbr/>Allowed</span><a href="#ExtrasAllowed" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><g stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" id="icon-anchor"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5"></path><path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5"></path></g></svg></a></h3>Re-exports <a href="../types/_truffle_codec.ExtrasAllowed.html">ExtrasAllowed</a></section>
<section class="tsd-panel tsd-member"><a id="StateVariable" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><span>State<wbr/>Variable</span><a href="#StateVariable" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>Re-exports <a href="../interfaces/_truffle_codec.StateVariable.html">StateVariable</a></section></section></div>
<div class="col-sidebar">
<div class="page-menu">
<div class="tsd-navigation settings">
<details class="tsd-index-accordion"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M4.93896 8.531L12 15.591L19.061 8.531L16.939 6.409L12 11.349L7.06098 6.409L4.93896 8.531Z" fill="var(--color-text)" id="icon-chevronDown"></path></svg>Settings</h3></summary>
<div class="tsd-accordion-details">
<div class="tsd-filter-visibility">
<h4 class="uppercase">Member Visibility</h4><form>
<ul id="tsd-filter-options">
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></form></div>
<div class="tsd-theme-toggle">
<h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div>
<details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg>On This Page</h3></summary>
<div class="tsd-accordion-details">
<ul>
<li>
<ul>
<li><a href="#md:truffle-decoder"><span>Truffle <wbr/>Decoder</span></a></li>
<li>
<ul>
<li><a href="#md:usage"><span>Usage</span></a></li>
<li>
<ul>
<li><a href="#md:initialization"><span>Initialization</span></a></li>
<li><a href="#md:decoder-methods"><span>Decoder methods</span></a></li>
<li><a href="#md:output-format-information"><span>Output format information</span></a></li>
<li><a href="#md:decoding-modes-abification-and-caveats"><span>Decoding modes, abification, and caveats</span></a></li>
<li><a href="#md:basic-usage-examples"><span>Basic usage examples</span></a></li>
<li>
<ul>
<li><a href="#md:decoding-a-log-with-the-project-decoder"><span>Decoding a log with the project decoder</span></a></li>
<li><a href="#md:decoding-state-variables-with-the-contract-instance-decoder"><span>Decoding state variables with the contract instance decoder</span></a></li></ul></li></ul></li></ul></li></ul></li>
<li><a href="#ExtrasAllowed" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>Extras<wbr/>Allowed</span></a></li>
<li><a href="#StateVariable" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>State<wbr/>Variable</span></a></li></ul></div></details></div>
<div class="site-menu">
<nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-namespace)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.33 16V7.24H10.77L13.446 14.74C13.43 14.54 13.41 14.296 13.386 14.008C13.37 13.712 13.354 13.404 13.338 13.084C13.33 12.756 13.326 12.448 13.326 12.16V7.24H14.37V16H12.93L10.266 8.5C10.282 8.692 10.298 8.936 10.314 9.232C10.33 9.52 10.342 9.828 10.35 10.156C10.366 10.476 10.374 10.784 10.374 11.08V16H9.33Z" fill="var(--color-text)"></path></g></svg><span>Truffle <wbr/>Decoding and <wbr/>Encoding</span></a>
<ul class="tsd-small-nested-navigation">
<li><a href="_truffle_codec.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/codec</span></a></li>
<li><a href="_truffle_encoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/encoder</span></a></li>
<li>
<details class="tsd-index-accordion" open data-key="decoder/lib"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="decoder_lib.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>decoder/lib</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li><a href="decoder_lib.html#ExtrasAllowed"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>Extras<wbr/>Allowed</span></a></li>
<li><a href="decoder_lib.html#StateVariable"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>State<wbr/>Variable</span></a></li>
<li><a href="../classes/decoder_lib.ContractAllocationFailedError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Allocation<wbr/>Failed<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ContractBeingDecodedHasNoNodeError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Being<wbr/>Decoded<wbr/>Has<wbr/>No<wbr/>Node<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ContractDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.ContractInstanceDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Instance<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.ContractNotFoundError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Found<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.InvalidAddressError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Address<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.NoProviderError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Provider<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ProjectDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Project<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.VariableNotFoundError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Variable<wbr/>Not<wbr/>Found<wbr/>Error</span></a></li>
<li><a href="../interfaces/decoder_lib.ContractState.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Contract<wbr/>State</span></a></li>
<li><a href="../interfaces/decoder_lib.DecodeLogOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decode<wbr/>Log<wbr/>Options</span></a></li>
<li><a href="../interfaces/decoder_lib.DecodedLog.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoded<wbr/>Log</span></a></li>
<li><a href="../interfaces/decoder_lib.DecoderSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoder<wbr/>Settings</span></a></li>
<li><a href="../interfaces/decoder_lib.EnsSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Ens<wbr/>Settings</span></a></li>
<li><a href="../interfaces/decoder_lib.EventOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Event<wbr/>Options</span></a></li>
<li><a href="../interfaces/decoder_lib.Log.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Log</span></a></li>
<li><a href="../interfaces/decoder_lib.ReturnOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Return<wbr/>Options</span></a></li>
<li><a href="../interfaces/decoder_lib.Transaction.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Transaction</span></a></li>
<li><a href="../types/decoder_lib.ProjectInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4194304"></use></svg><span>Project<wbr/>Info</span></a></li>
<li><a href="../functions/decoder_lib.forAddress.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Address</span></a></li>
<li><a href="../functions/decoder_lib.forArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact</span></a></li>
<li><a href="../functions/decoder_lib.forArtifactAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact<wbr/>At</span></a></li>
<li><a href="../functions/decoder_lib.forContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract</span></a></li>
<li><a href="../functions/decoder_lib.forContractAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>At</span></a></li>
<li><a href="../functions/decoder_lib.forContractInstance.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>Instance</span></a></li>
<li><a href="../functions/decoder_lib.forDeployedArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Artifact</span></a></li>
<li><a href="../functions/decoder_lib.forDeployedContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Contract</span></a></li>
<li><a href="../functions/decoder_lib.forProject.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project</span></a></li></ul></div></details></li></ul></nav></div></div></div>
<div class="tsd-generator">
<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div>
<div class="overlay"></div></body></html>