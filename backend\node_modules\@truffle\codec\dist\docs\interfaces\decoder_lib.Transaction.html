<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>Transaction | Truffle Decoding and Encoding</title><meta name="description" content="Documentation for Truffle Decoding and Encoding"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/search.js" id="tsd-search-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os"</script><header class="tsd-page-toolbar">
<div class="tsd-toolbar-contents container">
<div class="table-cell" id="tsd-search" data-base="..">
<div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M15.7824 13.833L12.6666 10.7177C12.5259 10.5771 12.3353 10.499 12.1353 10.499H11.6259C12.4884 9.39596 13.001 8.00859 13.001 6.49937C13.001 2.90909 10.0914 0 6.50048 0C2.90959 0 0 2.90909 0 6.49937C0 10.0896 2.90959 12.9987 6.50048 12.9987C8.00996 12.9987 9.39756 12.4863 10.5008 11.6239V12.1332C10.5008 12.3332 10.5789 12.5238 10.7195 12.6644L13.8354 15.7797C14.1292 16.0734 14.6042 16.0734 14.8948 15.7797L15.7793 14.8954C16.0731 14.6017 16.0731 14.1267 15.7824 13.833ZM6.50048 10.499C4.29094 10.499 2.50018 8.71165 2.50018 6.49937C2.50018 4.29021 4.28781 2.49976 6.50048 2.49976C8.71001 2.49976 10.5008 4.28708 10.5008 6.49937C10.5008 8.70852 8.71314 10.499 6.50048 10.499Z" fill="var(--color-text)"></path></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div>
<div class="field">
<div id="tsd-toolbar-links"></div></div>
<ul class="results">
<li class="state loading">Preparing search index...</li>
<li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Truffle Decoding and Encoding</a></div>
<div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><rect x="1" y="3" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="7" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="11" width="14" height="2" fill="var(--color-text)"></rect></svg></a></div></div></header>
<div class="container container-main">
<div class="col-content">
<div class="tsd-page-title">
<ul class="tsd-breadcrumb">
<li><a href="../index.html">Truffle Decoding and Encoding</a></li>
<li><a href="../modules/decoder_lib.html">decoder/lib</a></li>
<li><a href="decoder_lib.Transaction.html">Transaction</a></li></ul>
<h1>Interface Transaction</h1></div>
<section class="tsd-panel tsd-comment">
<div class="tsd-comment tsd-typography"><p>Contains information about a transaction.  Most of the fields have
been made optional; only those needed by the decoder have been made
mandatory.</p>
<p>Intended to work like Web3&#39;s
<a href="https://web3js.readthedocs.io/en/v1.2.1/web3-eth.html#eth-gettransaction-return">Transaction</a>
type, but with strings allowed where it requires numbers.</p>
</div>
<div class="tsd-comment tsd-typography"></div></section>
<section class="tsd-panel tsd-hierarchy">
<h4>Hierarchy</h4>
<ul class="tsd-hierarchy">
<li><span class="target">Transaction</span></li></ul></section><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L290">decoder/lib/types.ts:290</a></li></ul></aside>
<section class="tsd-panel-group tsd-index-group">
<section class="tsd-panel tsd-index-panel">
<details class="tsd-index-content tsd-index-accordion" open><summary class="tsd-accordion-summary tsd-index-summary">
<h5 class="tsd-index-heading uppercase" role="button" aria-expanded="false" tabIndex=0><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M1.5 5.50969L8 11.6609L14.5 5.50969L12.5466 3.66086L8 7.96494L3.45341 3.66086L1.5 5.50969Z" fill="var(--color-text)"></path></svg> Index</h5></summary>
<div class="tsd-accordion-details">
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Properties</h3>
<div class="tsd-index-list"><a href="decoder_lib.Transaction.html#blockHash" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-1024"><rect fill="var(--color-icon-background)" stroke="#FF984D" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M9.354 16V7.24H12.174C12.99 7.24 13.638 7.476 14.118 7.948C14.606 8.412 14.85 9.036 14.85 9.82C14.85 10.604 14.606 11.232 14.118 11.704C13.638 12.168 12.99 12.4 12.174 12.4H10.434V16H9.354ZM10.434 11.428H12.174C12.646 11.428 13.022 11.284 13.302 10.996C13.59 10.7 13.734 10.308 13.734 9.82C13.734 9.324 13.59 8.932 13.302 8.644C13.022 8.356 12.646 8.212 12.174 8.212H10.434V11.428Z" fill="var(--color-text)"></path></g></svg><span>block<wbr/>Hash?</span></a>
<a href="decoder_lib.Transaction.html#blockNumber" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>block<wbr/>Number</span></a>
<a href="decoder_lib.Transaction.html#from" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>from?</span></a>
<a href="decoder_lib.Transaction.html#gas" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>gas?</span></a>
<a href="decoder_lib.Transaction.html#gasPrice" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>gas<wbr/>Price?</span></a>
<a href="decoder_lib.Transaction.html#hash" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>hash?</span></a>
<a href="decoder_lib.Transaction.html#input" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>input</span></a>
<a href="decoder_lib.Transaction.html#maxFeePerGas" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>max<wbr/>Fee<wbr/>Per<wbr/>Gas?</span></a>
<a href="decoder_lib.Transaction.html#maxPriorityFeePerGas" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>max<wbr/>Priority<wbr/>Fee<wbr/>Per<wbr/>Gas?</span></a>
<a href="decoder_lib.Transaction.html#nonce" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>nonce?</span></a>
<a href="decoder_lib.Transaction.html#to" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>to</span></a>
<a href="decoder_lib.Transaction.html#transactionIndex" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>transaction<wbr/>Index?</span></a>
<a href="decoder_lib.Transaction.html#type" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>type?</span></a>
<a href="decoder_lib.Transaction.html#value" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>value?</span></a>
</div></section></div></details></section></section>
<section class="tsd-panel-group tsd-member-group">
<h2>Properties</h2>
<section class="tsd-panel tsd-member"><a id="blockHash" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>block<wbr/>Hash</span><a href="#blockHash" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><g stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round" id="icon-anchor"><path stroke="none" d="M0 0h24v24H0z" fill="none"></path><path d="M10 14a3.5 3.5 0 0 0 5 0l4 -4a3.5 3.5 0 0 0 -5 -5l-.5 .5"></path><path d="M14 10a3.5 3.5 0 0 0 -5 0l-4 4a3.5 3.5 0 0 0 5 5l.5 -.5"></path></g></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">block<wbr/>Hash</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Hash of this transaction&#39;s block as hex string; null if pending.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L302">decoder/lib/types.ts:302</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="blockNumber" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><span>block<wbr/>Number</span><a href="#blockNumber" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">block<wbr/>Number</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">bigint</span></div>
<div class="tsd-comment tsd-typography"><p>This transaction&#39;s block number; null if pending.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L306">decoder/lib/types.ts:306</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="from" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>from</span><a href="#from" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">from</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Address of the sender (as checksummed hex string).</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L314">decoder/lib/types.ts:314</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="gas" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>gas</span><a href="#gas" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">gas</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Gas provided by the sender, as numeric string.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L331">decoder/lib/types.ts:331</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="gasPrice" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>gas<wbr/>Price</span><a href="#gasPrice" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">gas<wbr/>Price</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Gas price for this transaction, as numeric string.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L327">decoder/lib/types.ts:327</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="hash" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>hash</span><a href="#hash" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">hash</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>The transaction hash as hex string.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L294">decoder/lib/types.ts:294</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="input" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><span>input</span><a href="#input" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">input</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Data sent with the transaction, as hex string.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L335">decoder/lib/types.ts:335</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="maxFeePerGas" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>max<wbr/>Fee<wbr/>Per<wbr/>Gas</span><a href="#maxFeePerGas" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">max<wbr/>Fee<wbr/>Per<wbr/>Gas</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>The maximum fee per gas that the transaction is willing to pay in total.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L343">decoder/lib/types.ts:343</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="maxPriorityFeePerGas" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>max<wbr/>Priority<wbr/>Fee<wbr/>Per<wbr/>Gas</span><a href="#maxPriorityFeePerGas" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">max<wbr/>Priority<wbr/>Fee<wbr/>Per<wbr/>Gas</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>The maximum fee per gas to give miners to incentivize them to include the transaction (Priority fee).</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L347">decoder/lib/types.ts:347</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="nonce" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>nonce</span><a href="#nonce" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">nonce</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">bigint</span></div>
<div class="tsd-comment tsd-typography"><p>The nonce of the sender before this transaction was sent.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L298">decoder/lib/types.ts:298</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="to" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><span>to</span><a href="#to" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">to</span><span class="tsd-signature-symbol">:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Address of the recipient (as checksummed hex string), or null for a
contract creation.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L319">decoder/lib/types.ts:319</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="transactionIndex" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>transaction<wbr/>Index</span><a href="#transactionIndex" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">transaction<wbr/>Index</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">bigint</span></div>
<div class="tsd-comment tsd-typography"><p>Index of transaction in block; null if block is pending.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L310">decoder/lib/types.ts:310</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="type" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>type</span><a href="#type" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">type</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">number</span><span class="tsd-signature-symbol"> | </span><span class="tsd-signature-type">bigint</span></div>
<div class="tsd-comment tsd-typography"><p>A positive unsigned 8-bit number between 0 and 0x7f that represents the type of the transaction.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L339">decoder/lib/types.ts:339</a></li></ul></aside></section>
<section class="tsd-panel tsd-member"><a id="value" class="tsd-anchor"></a>
<h3 class="tsd-anchor-link"><code class="tsd-tag ts-flagOptional">Optional</code> <span>value</span><a href="#value" aria-label="Permalink" class="tsd-anchor-icon"><svg viewBox="0 0 24 24"><use href="#icon-anchor"></use></svg></a></h3>
<div class="tsd-signature"><span class="tsd-kind-property">value</span><span class="tsd-signature-symbol">?:</span> <span class="tsd-signature-type">string</span></div>
<div class="tsd-comment tsd-typography"><p>Wei sent with this transaction, as numeric string.</p>
</div>
<div class="tsd-comment tsd-typography"></div><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/decoder/lib/types.ts#L323">decoder/lib/types.ts:323</a></li></ul></aside></section></section></div>
<div class="col-sidebar">
<div class="page-menu">
<div class="tsd-navigation settings">
<details class="tsd-index-accordion"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M4.93896 8.531L12 15.591L19.061 8.531L16.939 6.409L12 11.349L7.06098 6.409L4.93896 8.531Z" fill="var(--color-text)" id="icon-chevronDown"></path></svg>Settings</h3></summary>
<div class="tsd-accordion-details">
<div class="tsd-filter-visibility">
<h4 class="uppercase">Member Visibility</h4><form>
<ul id="tsd-filter-options">
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></form></div>
<div class="tsd-theme-toggle">
<h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div>
<details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg>On This Page</h3></summary>
<div class="tsd-accordion-details">
<ul>
<li><a href="#blockHash" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>block<wbr/>Hash</span></a></li>
<li><a href="#blockNumber" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>block<wbr/>Number</span></a></li>
<li><a href="#from" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>from</span></a></li>
<li><a href="#gas" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>gas</span></a></li>
<li><a href="#gasPrice" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>gas<wbr/>Price</span></a></li>
<li><a href="#hash" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>hash</span></a></li>
<li><a href="#input" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>input</span></a></li>
<li><a href="#maxFeePerGas" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>max<wbr/>Fee<wbr/>Per<wbr/>Gas</span></a></li>
<li><a href="#maxPriorityFeePerGas" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>max<wbr/>Priority<wbr/>Fee<wbr/>Per<wbr/>Gas</span></a></li>
<li><a href="#nonce" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>nonce</span></a></li>
<li><a href="#to" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>to</span></a></li>
<li><a href="#transactionIndex" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>transaction<wbr/>Index</span></a></li>
<li><a href="#type" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>type</span></a></li>
<li><a href="#value" class=""><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-1024"></use></svg><span>value</span></a></li></ul></div></details></div>
<div class="site-menu">
<nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-namespace)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.33 16V7.24H10.77L13.446 14.74C13.43 14.54 13.41 14.296 13.386 14.008C13.37 13.712 13.354 13.404 13.338 13.084C13.33 12.756 13.326 12.448 13.326 12.16V7.24H14.37V16H12.93L10.266 8.5C10.282 8.692 10.298 8.936 10.314 9.232C10.33 9.52 10.342 9.828 10.35 10.156C10.366 10.476 10.374 10.784 10.374 11.08V16H9.33Z" fill="var(--color-text)"></path></g></svg><span>Truffle <wbr/>Decoding and <wbr/>Encoding</span></a>
<ul class="tsd-small-nested-navigation">
<li><a href="../modules/_truffle_codec.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/codec</span></a></li>
<li><a href="../modules/_truffle_encoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/encoder</span></a></li>
<li>
<details class="tsd-index-accordion" open data-key="decoder/lib"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="../modules/decoder_lib.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>decoder/lib</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li><a href="../modules/decoder_lib.html#ExtrasAllowed"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-8388608"><rect fill="var(--color-icon-background)" stroke="#FF4D82" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="12"></rect><path d="M10.354 17V8.24H13.066C13.586 8.24 14.042 8.348 14.434 8.564C14.826 8.772 15.13 9.064 15.346 9.44C15.562 9.816 15.67 10.256 15.67 10.76C15.67 11.352 15.514 11.86 15.202 12.284C14.898 12.708 14.482 13 13.954 13.16L15.79 17H14.518L12.838 13.28H11.434V17H10.354ZM11.434 12.308H13.066C13.514 12.308 13.874 12.168 14.146 11.888C14.418 11.6 14.554 11.224 14.554 10.76C14.554 10.288 14.418 9.912 14.146 9.632C13.874 9.352 13.514 9.212 13.066 9.212H11.434V12.308Z" fill="var(--color-text)"></path></g></svg><span>Extras<wbr/>Allowed</span></a></li>
<li><a href="../modules/decoder_lib.html#StateVariable"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-8388608"></use></svg><span>State<wbr/>Variable</span></a></li>
<li><a href="../classes/decoder_lib.ContractAllocationFailedError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-128"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-class)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g></svg><span>Contract<wbr/>Allocation<wbr/>Failed<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ContractBeingDecodedHasNoNodeError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Being<wbr/>Decoded<wbr/>Has<wbr/>No<wbr/>Node<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ContractDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.ContractInstanceDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Instance<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.ContractNotFoundError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Found<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.InvalidAddressError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Address<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.NoProviderError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Provider<wbr/>Error</span></a></li>
<li><a href="../classes/decoder_lib.ProjectDecoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Project<wbr/>Decoder</span></a></li>
<li><a href="../classes/decoder_lib.VariableNotFoundError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Variable<wbr/>Not<wbr/>Found<wbr/>Error</span></a></li>
<li><a href="decoder_lib.ContractState.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-256"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-interface)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.51 16V15.016H11.298V8.224H9.51V7.24H14.19V8.224H12.402V15.016H14.19V16H9.51Z" fill="var(--color-text)"></path></g></svg><span>Contract<wbr/>State</span></a></li>
<li><a href="decoder_lib.DecodeLogOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decode<wbr/>Log<wbr/>Options</span></a></li>
<li><a href="decoder_lib.DecodedLog.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoded<wbr/>Log</span></a></li>
<li><a href="decoder_lib.DecoderSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Decoder<wbr/>Settings</span></a></li>
<li><a href="decoder_lib.EnsSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Ens<wbr/>Settings</span></a></li>
<li><a href="decoder_lib.EventOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Event<wbr/>Options</span></a></li>
<li><a href="decoder_lib.Log.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Log</span></a></li>
<li><a href="decoder_lib.ReturnOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Return<wbr/>Options</span></a></li>
<li><a href="decoder_lib.Transaction.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Transaction</span></a></li>
<li><a href="../types/decoder_lib.ProjectInfo.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4194304"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-type-alias)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.31 16V8.224H8.91V7.24H14.79V8.224H12.39V16H11.31Z" fill="var(--color-text)"></path></g></svg><span>Project<wbr/>Info</span></a></li>
<li><a href="../functions/decoder_lib.forAddress.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-64"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g></svg><span>for<wbr/>Address</span></a></li>
<li><a href="../functions/decoder_lib.forArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact</span></a></li>
<li><a href="../functions/decoder_lib.forArtifactAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact<wbr/>At</span></a></li>
<li><a href="../functions/decoder_lib.forContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract</span></a></li>
<li><a href="../functions/decoder_lib.forContractAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>At</span></a></li>
<li><a href="../functions/decoder_lib.forContractInstance.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>Instance</span></a></li>
<li><a href="../functions/decoder_lib.forDeployedArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Artifact</span></a></li>
<li><a href="../functions/decoder_lib.forDeployedContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Contract</span></a></li>
<li><a href="../functions/decoder_lib.forProject.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project</span></a></li></ul></div></details></li></ul></nav></div></div></div>
<div class="tsd-generator">
<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div>
<div class="overlay"></div></body></html>