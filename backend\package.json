{"name": "ecoxchange-backend", "version": "1.0.0", "description": "EcoXChange Backend API Server", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["carbon-credits", "blockchain", "api"], "author": "", "license": "ISC", "dependencies": {"@truffle/contract": "^4.6.31", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "ipfs-http-client": "^60.0.1", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.1", "pg": "^8.16.2", "sequelize": "^6.37.7", "web3": "^4.16.0"}, "devDependencies": {"nodemon": "^3.0.2"}}