{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../lib/abi-data/allocate/utils.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,+BAA+B,CAAC,CAAC;AAM3D,iEAA4D;AAC5D,4DAAgD;AAEhD,yDAAoD;AACpD,wDAA0C;AAS1C,SAAgB,qBAAqB,CACnC,YAAwC;IAExC,IAAI,QAAQ,GAAsB,EAAE,CAAC;IACrC,IAAI,gBAAgB,GAAsB,EAAE,CAAC;IAC7C,IAAI,oBAAoB,GAA0B,EAAE,CAAC;IACrD,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;QACtC,KAAK,MAAM,QAAQ,IAAI,WAAW,CAAC,SAAS,EAAE;YAC5C,MAAM,IAAI,GAAgB,YAAY,CAAC,KAAK,CAAC,eAAe,CAC1D,QAAQ,EACR,WAAW,CACZ,CAAC;YACF,IAAI,eAAe,GAAiC,SAAS,CAAC;YAC9D,IAAI,kBAAkB,GAAiC,SAAS,CAAC;YACjE,MAAM,gBAAgB,GAAG,sBAAK,CAAC,WAAW,CAAC,WAAW,CACpD,QAAQ,CAAC,gBAAgB,CAC1B,CAAC;YACF,MAAM,QAAQ,GAAG,sBAAK,CAAC,WAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAClE,IAAI,gBAAgB,IAAI,gBAAgB,KAAK,IAAI,EAAE;gBACjD,eAAe,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAC1C,QAAQ,EACR,IAAI,EACJ,WAAW,CACZ,CAAC;gBACF,QAAQ,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,eAAe,CAAC;gBACpD,uEAAuE;aACxE;YACD,IAAI,QAAQ,IAAI,QAAQ,KAAK,IAAI,EAAE;gBACjC,kBAAkB,GAAG,QAAQ,CAAC,KAAK,CAAC,WAAW,CAC7C,QAAQ,EACR,IAAI,EACJ,WAAW,EACX,IAAI,CACL,CAAC;gBACF,QAAQ,CAAC,kBAAkB,CAAC,OAAO,CAAC,GAAG,kBAAkB,CAAC;aAC3D;YACD,oBAAoB,CAAC,IAAI,CAAC;gBACxB,QAAQ;gBACR,IAAI;gBACJ,eAAe;gBACf,kBAAkB;gBAClB,aAAa,EAAE,WAAW,CAAC,EAAE;aAC9B,CAAC,CAAC;SACJ;KACF;IACD,KAAK,CAAC,oBAAoB,EAAE,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEnD,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IACtD,gBAAgB,GAAG,MAAM,CAAC,MAAM,CAC9B,EAAE,EACF,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CACvC,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE,CAC7D,CACF,CAAC;IAEF,KAAK,MAAM,mBAAmB,IAAI,oBAAoB,EAAE;QACtD,yCAAyC;QACzC,IAAI,mBAAmB,CAAC,eAAe,EAAE;YACvC,mBAAmB,CAAC,eAAe;gBACjC,QAAQ,CAAC,mBAAmB,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;SAClF;QACD,IAAI,mBAAmB,CAAC,kBAAkB,EAAE;YAC1C,mBAAmB,CAAC,kBAAkB;gBACpC,QAAQ,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB;SACrF;KACF;IAED,MAAM,cAAc,GAA6B,oBAAoB,CAAC,GAAG,CACvE,CAAC,EACC,QAAQ,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,mBAAmB,EAAE,EAChD,aAAa,EACb,IAAI,EACJ,eAAe,EACf,kBAAkB,EACnB,EAAE,EAAE,CAAC,CAAC;QACL,GAAG,EAAE,GAAG,CAAC,SAAS,CAAC,GAAG,CAAC;QACvB,aAAa;QACb,QAAQ;QACR,YAAY,EAAE,IAAI;QAClB,eAAe;QACf,kBAAkB;QAClB,mBAAmB;KACpB,CAAC,CACH,CAAC;IAEF,OAAO;QACL,QAAQ;QACR,gBAAgB;QAChB,oBAAoB;QACpB,cAAc;KACf,CAAC;AACJ,CAAC;AA3FD,sDA2FC"}