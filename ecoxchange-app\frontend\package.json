{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@web3-react/core": "^8.2.3", "@web3-react/injected-connector": "^6.0.7", "@web3-react/walletconnect-connector": "^6.2.13", "ethers": "^6.14.4", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "web3": "^4.16.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.4", "tailwindcss": "^4", "typescript": "^5"}}