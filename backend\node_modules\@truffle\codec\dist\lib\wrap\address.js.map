{"version": 3, "file": "address.js", "sourceRoot": "", "sources": ["../../../lib/wrap/address.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAAgC;AAChC,MAAM,KAAK,GAAG,IAAA,eAAW,EAAC,oBAAoB,CAAC,CAAC;AAGhD,yCAA2C;AAC3C,qCAAmE;AAQnE,+CAAiC;AACjC,uDAAqD;AACrD,qDAAuC;AACvC,4DAAmC;AAEnC,8DAA8D;AAC9D,mCAAmC;AAEnC,MAAM,sBAAsB,GAItB;IACJ,oBAAoB;IACpB,8BAA8B;IAC9B,sBAAsB,CAAC,sDAAsD;CAC9E,CAAC;AAEF,MAAM,iBAAiB,GAIjB;IACJ,GAAG,sBAAsB;IACzB,sBAAsB;IACtB,wBAAwB;IACxB,gCAAgC;IAChC,yBAAyB;IACzB,kBAAkB;CACnB,CAAC;AAEW,QAAA,YAAY,GAInB,CAAC,yBAAyB,EAAE,GAAG,iBAAiB,CAAC,CAAC;AAExD,QAAQ,CAAC,CAAC,oBAAoB,CAC5B,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wBAAwB,CACzB,CAAC;KACH;IACD,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wCAAwC,CACzC,CAAC;KACH;IACD,OAAO,wBAAwB,CAAC,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,CAAC,IAAI,CAAC,CAAC;AAC5E,CAAC;AAED,QAAQ,CAAC,CAAC,8BAA8B,CACtC,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wBAAwB,CACzB,CAAC;KACH;IACD,IAAI,CAAC,KAAK,CAAC,qBAAqB,CAAC,KAAK,CAAC,EAAE;QACvC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wCAAwC,CACzC,CAAC;KACH;IACD,OAAO,wBAAwB,CAC7B,QAAQ,EACR,IAAI,GAAG,KAAK,EACZ,KAAK,EACL,WAAW,CAAC,IAAI,CACjB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,sBAAsB,CAC9B,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,wBAAwB,CACzB,CAAC;KACH;IACD,MAAM,OAAO,GAAG,EAAE,IAAI,EAAE,SAAkB,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;IAC1D,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC;IAC/B,IAAI,QAAQ,CAAC,IAAI,KAAK,SAAS,EAAE;QAC/B,MAAM,IAAI,6BAAoB,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;KACnD;IACD,IAAI,QAAQ,CAAC,OAAO,KAAK,IAAI,EAAE;QAC7B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,QAAQ,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EACpC,QAAQ,CAAC,MAAM,CAChB,CAAC;KACH;IACD,2DAA2D;IAC3D,OAAO,qBAAqB,CAAC,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC3D,CAAC;AAED,QAAQ,CAAC,CAAC,sBAAsB,CAC9B,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QAC/B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,8BAA8B,CAC/B,CAAC;KACH;IACD,qBAAqB;IACrB,OAAO,KAAK,CAAC,CAAC,IAAA,wBAAa,EACzB,QAAQ,EACR,KAAK,CAAC,OAAO,EAAE,EACf,WAAW,EACX,sBAAsB,CACvB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,wBAAwB,CAChC,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QACjC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,sCAAsC,CACvC,CAAC;KACH;IACD,OAAO,wBAAwB,CAC7B,QAAQ,EACR,KAAK,CAAC,OAAO,EACb,KAAK,EACL,WAAW,CAAC,IAAI,CACjB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,gCAAgC,CACxC,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QACjC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,gCAAgC,CACjC,CAAC;KACH;IACD,IACE,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,SAAS;QAClC,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,UAAU,EACnC;QACA,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CACxC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;QAC1B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAC5B,CAAC;KACH;IACD,IAAI,OAAe,CAAC;IACpB,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;QAC5B,KAAK,SAAS;YACZ,OAAO,GAAgC,KAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YAC9D,MAAM;QACR,KAAK,UAAU;YACb,OAAO,GAAiC,KAAM,CAAC,KAAK,CAAC,OAAO,CAAC;YAC7D,MAAM;QACR,0CAA0C;KAC3C;IACD,yDAAyD;IACzD,OAAO,qBAAqB,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;AAClD,CAAC;AAED,QAAQ,CAAC,CAAC,yBAAyB,CACjC,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;QACjC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,gCAAgC,CACjC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,KAAK,sBAAsB,EAAE;QACnD,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAAC,KAAK,CAAC,IAAI,CAAC,CACxC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,OAAO,EAAE;QAC1B,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,kBAAkB,CAC5B,CAAC;KACH;IACD,OAAO,KAAK,CAAC,CAAC,gCAAgC,CAC5C,QAAQ,EACR,KAAK,CAAC,KAAK,EACX,WAAW,CACZ,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,yBAAyB,CACjC,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE;QAClC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,iCAAiC,CAClC,CAAC;KACH;IACD,IAAI,KAAK,CAAC,IAAI,KAAK,SAAS,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;QACzD,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,QAAQ,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,CAC1C,CAAC;KACH;IACD,wDAAwD;IACxD,OAAO,KAAK,CAAC,CAAC,IAAA,wBAAa,EACzB,QAAQ,EACR,KAAK,CAAC,KAAK,kCACN,WAAW,KAAE,KAAK,EAAE,IAAI,KAC7B,iBAAiB,CAClB,CAAC;AACJ,CAAC;AAED,QAAQ,CAAC,CAAC,kBAAkB,CAC1B,QAAyB,EACzB,KAAc,EACd,WAAwB;IAExB,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,WAAW,CAAC,IAAI,EAChB,CAAC,EACD,0CAA0C,CAC3C,CAAC;AACJ,CAAC;AAED,SAAS,oBAAoB,CAC3B,SAAiB,EACjB,QAA2B,EAAE,YAAY;AACzC,KAAc,EAAE,YAAY;AAC5B,IAAY,CAAC,YAAY;;IAEzB,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,EAAE;QAClC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,CAAC,EACD,QAAQ,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAC1C,CAAC;KACH;IACD,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC,GAAG,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE;QACtD,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,CAAC,EACD,QAAQ,CAAC,kBAAkB,CACzB,SAAS,EACT,QAAQ,CAAC,YAAY,EACrB,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,CAAC,CAC3B,CACF,CAAC;KACH;IACD,IAAI,CAAC,oBAAS,CAAC,SAAS,CAAC,SAAS,CAAC,EAAE;QACnC,MAAM,IAAI,0BAAiB,CACzB,QAAQ,EACR,KAAK,EACL,IAAI,EACJ,CAAC,EAAE,wCAAwC;QAC3C,QAAQ,CAAC,qBAAqB,CAC/B,CAAC;KACH;IACD,eAAe;IACf,OAAO,oBAAS,CAAC,iBAAiB,CAAC,SAAS,CAAC,CAAC;AAChD,CAAC;AAED,SAAS,qBAAqB,CAC5B,QAAyB,EACzB,SAAiB;IAEjB,iDAAiD;IACjD,QAAQ,QAAQ,CAAC,SAAS,EAAE;QAC1B,KAAK,SAAS;YACZ,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,OAAgB;gBACtB,KAAK,EAAE;oBACL,SAAS;iBACV;gBACD,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,KAAK,UAAU;YACb,OAAO;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,OAAgB;gBACtB,KAAK,EAAE;oBACL,IAAI,EAAE,SAAkB;oBACxB,OAAO,EAAE,SAAS;iBACnB;gBACD,eAAe,EAAE,EAAE;aACpB,CAAC;KACL;AACH,CAAC;AAED,SAAS,wBAAwB,CAC/B,QAAyB,EACzB,SAAiB,EACjB,KAAc,EAAE,YAAY;AAC5B,IAAY,CAAC,YAAY;;IAEzB,OAAO,qBAAqB,CAC1B,QAAQ,EACR,oBAAoB,CAAC,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,CACvD,CAAC;AACJ,CAAC"}