import * as Ast from "../ast";
import type * as Abi from "@truffle/abi-utils";
import { abiSignature, abiTypeSignature, abiTupleSignature, abiSelector } from "@truffle/abi-utils";
import type { FunctionAbiBySelectors } from "./types";
export { abiSignature, abiTypeSignature, abiTupleSignature, abiSelector };
export declare const DEFAULT_CONSTRUCTOR_ABI: Abi.ConstructorEntry;
export declare function computeSelectors(abi: Abi.Abi | undefined): FunctionAbiBySelectors | undefined;
export declare function abiHasPayableFallback(abi: Abi.Abi | undefined): boolean | undefined;
export declare function abisMatch(entry1: Abi.Entry | undefined, entry2: Abi.Entry | undefined): boolean;
export declare function definitionMatchesAbi(abiEntry: Abi.Entry, definition: Ast.AstNode, referenceDeclarations: Ast.AstNodes): boolean;
export declare function topicsCount(abiEntry: Abi.EventEntry): number;
export declare function abiEntryIsObviouslyIllTyped(abiEntry: Abi.Entry): boolean;
export declare function abiEntryHasStorageParameters(abiEntry: Abi.Entry): boolean;
