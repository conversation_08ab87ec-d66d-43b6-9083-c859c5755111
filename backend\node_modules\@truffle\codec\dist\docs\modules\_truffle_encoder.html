<!DOCTYPE html><html class="default" lang="en"><head><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="IE=edge"/><title>@truffle/encoder | Truffle Decoding and Encoding</title><meta name="description" content="Documentation for Truffle Decoding and Encoding"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="stylesheet" href="../assets/style.css"/><link rel="stylesheet" href="../assets/highlight.css"/><script defer src="../assets/main.js"></script><script async src="../assets/search.js" id="tsd-search-script"></script></head><body><script>document.documentElement.dataset.theme = localStorage.getItem("tsd-theme") || "os"</script><header class="tsd-page-toolbar">
<div class="tsd-toolbar-contents container">
<div class="table-cell" id="tsd-search" data-base="..">
<div class="field"><label for="tsd-search-field" class="tsd-widget tsd-toolbar-icon search no-caption"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M15.7824 13.833L12.6666 10.7177C12.5259 10.5771 12.3353 10.499 12.1353 10.499H11.6259C12.4884 9.39596 13.001 8.00859 13.001 6.49937C13.001 2.90909 10.0914 0 6.50048 0C2.90959 0 0 2.90909 0 6.49937C0 10.0896 2.90959 12.9987 6.50048 12.9987C8.00996 12.9987 9.39756 12.4863 10.5008 11.6239V12.1332C10.5008 12.3332 10.5789 12.5238 10.7195 12.6644L13.8354 15.7797C14.1292 16.0734 14.6042 16.0734 14.8948 15.7797L15.7793 14.8954C16.0731 14.6017 16.0731 14.1267 15.7824 13.833ZM6.50048 10.499C4.29094 10.499 2.50018 8.71165 2.50018 6.49937C2.50018 4.29021 4.28781 2.49976 6.50048 2.49976C8.71001 2.49976 10.5008 4.28708 10.5008 6.49937C10.5008 8.70852 8.71314 10.499 6.50048 10.499Z" fill="var(--color-text)"></path></svg></label><input type="text" id="tsd-search-field" aria-label="Search"/></div>
<div class="field">
<div id="tsd-toolbar-links"></div></div>
<ul class="results">
<li class="state loading">Preparing search index...</li>
<li class="state failure">The search index is not available</li></ul><a href="../index.html" class="title">Truffle Decoding and Encoding</a></div>
<div class="table-cell" id="tsd-widgets"><a href="#" class="tsd-widget tsd-toolbar-icon menu no-caption" data-toggle="menu" aria-label="Menu"><svg width="16" height="16" viewBox="0 0 16 16" fill="none"><rect x="1" y="3" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="7" width="14" height="2" fill="var(--color-text)"></rect><rect x="1" y="11" width="14" height="2" fill="var(--color-text)"></rect></svg></a></div></div></header>
<div class="container container-main">
<div class="col-content">
<div class="tsd-page-title">
<ul class="tsd-breadcrumb">
<li><a href="../index.html">Truffle Decoding and Encoding</a></li>
<li><a href="_truffle_encoder.html">@truffle/encoder</a></li></ul>
<h1>Module @truffle/encoder</h1></div>
<section class="tsd-panel tsd-comment">
<div class="tsd-comment tsd-typography"><a id="md:truffle-encoder" class="tsd-anchor"></a><h1><a href="#md:truffle-encoder">Truffle Encoder</a></h1><p>This module provides an interface for recognizing JavaScript user input of
Solidity values, encoding those values for use in a transaction, and performing
overload resolution based on those values to determine which Solidity method to
encode for.</p>
<p>The interface is split into three classes: The project encoder, the contract
encoder, and the contract instance encoder.  The project encoder is associated
to the project as a whole; it can recognize user input, encode transactions,
and resolve overloads, although the interface for the latter two is somewhat
inconvenient.  The contract encoder is associated to a specific contract class.
It is similar to the project encoder, but presents an easier-to-use interface
for transaction encoding and overload resolution, so long as one is dealing
with methods of the specified class.  The contract instance encoder is
associated to a specific contract instance; it is like the contract encoder,
but is associated to a specific address, allowing the <code>to</code> option in
transactions to be populated automatically.</p>
<a id="md:usage" class="tsd-anchor"></a><h2><a href="#md:usage">Usage</a></h2><a id="md:initialization" class="tsd-anchor"></a><h3><a href="#md:initialization">Initialization</a></h3><p>Create a encoder with one of the various constructor functions.</p>
<p>For a project encoder, use the [[forProject|<code>forProject</code>]] function.</p>
<p>For a contract encoder, use the [[forArtifact|<code>forArtifact</code>]] or
[[forContract|<code>forContract</code>]] function.</p>
<p>For a contract instance encoder, use one of the following:</p>
<ul>
<li>[[forDeployedArtifact|<code>forDeployedArtifact</code>]]</li>
<li>[[forDeployedContract|<code>forDeployedContract</code>]]</li>
<li>[[forArtifactAt|<code>forArtifactAt</code>]]</li>
<li>[[forContractAt|<code>forContractAt</code>]]</li>
<li>[[forContractInstance|<code>forContractInstance</code>]]</li>
</ul>
<p>See the documentation of these functions for details, or below for usage
examples.</p>
<p>All of these functions take a final argument in which information about the
project is specified; currently only a few methods for specifying project
information are allowed, but more are planned.</p>
<p>One can also spawn encoders from other encoders by supplying additional
information.  See the documentation for the individual encoder classes for a
method listing.</p>
<a id="md:encoder-methods" class="tsd-anchor"></a><h3><a href="#md:encoder-methods">Encoder methods</a></h3><p>See the documentation for the individual encoder classes for a method listing.</p>
<a id="md:wrapped-format-information" class="tsd-anchor"></a><h3><a href="#md:wrapped-format-information">Wrapped format information</a></h3><p>When using the various &quot;wrap&quot; functions, values will be wrapped in
machine-readable [[Format.Values.Value]] objects containing individual wrapped
values.  (This is the same format that <code>@truffle/decoder</code> produces output in.)
See the [[Format|format documentation]] for an overview and complete module
listing.</p>
<a id="md:use-of-project-information-and-encoding-of-enums" class="tsd-anchor"></a><h3><a href="#md:use-of-project-information-and-encoding-of-enums">Use of project information and encoding of enums</a></h3><p>The encoder can do purely ABI-based encoding, like other encoders; however it
has the capability to use project information to do more.</p>
<p>The most significant use of this is that if further project information is
present, this allows for enums to be entered as strings with the name of
the option, rather than having to be entered via the underlying number.
See the documentation of [[ProjectEncoder.wrap]] for more.</p>
<p>Similarly, if project information is present, the encoder will also throw an
error if you attempt to put an out-of-range value into an enum type, and
refuse to consider overloads that would result in this during overload
resolution.  If project information is absent, the encoder will be unable to
recognize any error in these situations.</p>
<a id="md:ens-resolution" class="tsd-anchor"></a><h3><a href="#md:ens-resolution">ENS resolution</a></h3><p>The encoder supports ENS resolution for address and contract types if
initialized to support such.  See the documentation of the [[EncoderSettings]]
and [[EnsSettings]] types for more.</p>
<a id="md:basic-usage-examples" class="tsd-anchor"></a><h3><a href="#md:basic-usage-examples">Basic usage examples</a></h3><p>These usage examples are for a project with two contracts, <code>Contract1</code> and
<code>Contract2</code>.  Let&#39;s suppose these look like the following:</p>
<pre><code class="language-solidity"><span class="hl-1">pragma</span><span class="hl-2"> </span><span class="hl-8">solidity</span><span class="hl-2"> ^0.8.0;</span><br/><br/><span class="hl-3">contract</span><span class="hl-9"> Contract1</span><span class="hl-2"> {</span><br/><span class="hl-2"> </span><span class="hl-3">function</span><span class="hl-2"> </span><span class="hl-6">enumExample</span><span class="hl-2">(</span><span class="hl-3">Contract2</span><span class="hl-2">.</span><span class="hl-4">Ternary</span><span class="hl-2"> </span><span class="hl-4">x</span><span class="hl-2">) </span><span class="hl-3">public</span><span class="hl-2"> </span><span class="hl-3">payable</span><span class="hl-2"> {</span><br/><span class="hl-2"> }</span><br/><br/><span class="hl-2"> </span><span class="hl-3">function</span><span class="hl-2"> </span><span class="hl-6">overloaded</span><span class="hl-2">(</span><span class="hl-9">uint</span><span class="hl-2"> </span><span class="hl-4">x</span><span class="hl-2">) </span><span class="hl-3">public</span><span class="hl-2"> </span><span class="hl-3">payable</span><span class="hl-2"> {</span><br/><span class="hl-2"> }</span><br/><br/><span class="hl-2"> </span><span class="hl-3">function</span><span class="hl-2"> </span><span class="hl-6">overloaded</span><span class="hl-2">(</span><span class="hl-9">string</span><span class="hl-2"> </span><span class="hl-4">x</span><span class="hl-2">) </span><span class="hl-3">public</span><span class="hl-2"> </span><span class="hl-3">payable</span><span class="hl-2"> {</span><br/><span class="hl-2"> }</span><br/><span class="hl-2">}</span><br/><br/><span class="hl-3">contract</span><span class="hl-9"> Contract2</span><span class="hl-2"> {</span><br/><span class="hl-2"> </span><span class="hl-3">enum</span><span class="hl-2"> </span><span class="hl-9">Ternary</span><span class="hl-2"> { </span><span class="hl-10">No</span><span class="hl-2">, </span><span class="hl-10">Yes</span><span class="hl-2">, </span><span class="hl-10">Maybe</span><span class="hl-2"> }</span><br/><span class="hl-2">}</span>
</code><button>Copy</button></pre>
<a id="md:encoding-a-transaction" class="tsd-anchor"></a><h4><a href="#md:encoding-a-transaction">Encoding a transaction</a></h4><pre><code class="language-typescript"><span class="hl-1">import</span><span class="hl-2"> { </span><span class="hl-4">forContract</span><span class="hl-2"> } </span><span class="hl-1">from</span><span class="hl-2"> </span><span class="hl-5">&quot;@truffle/encoder&quot;</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract1</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract1&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract2</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract2&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">encoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">Encoder</span><span class="hl-2">.</span><span class="hl-6">forContract</span><span class="hl-2">(</span><span class="hl-4">Contract1</span><span class="hl-2">, [</span><span class="hl-4">Contract1</span><span class="hl-2">, </span><span class="hl-4">Contract2</span><span class="hl-2">]);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">abi</span><span class="hl-2"> = </span><span class="hl-4">Contract1</span><span class="hl-2">.</span><span class="hl-4">abi</span><span class="hl-2">.</span><span class="hl-6">find</span><span class="hl-2">(</span><span class="hl-4">abiEntry</span><span class="hl-2"> </span><span class="hl-3">=&gt;</span><span class="hl-2"> </span><span class="hl-4">abiEntry</span><span class="hl-2">.</span><span class="hl-4">name</span><span class="hl-2"> === </span><span class="hl-5">&quot;enumExample&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">tx</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">encoder</span><span class="hl-2">.</span><span class="hl-6">encodeTransaction</span><span class="hl-2">(</span><br/><span class="hl-2"> </span><span class="hl-4">abi</span><span class="hl-2">,</span><br/><span class="hl-2"> [</span><span class="hl-5">&quot;Maybe&quot;</span><span class="hl-2">, { </span><span class="hl-4">value:</span><span class="hl-2"> </span><span class="hl-7">1</span><span class="hl-2"> }],</span><br/><span class="hl-2"> { </span><span class="hl-4">allowOptions:</span><span class="hl-2"> </span><span class="hl-3">true</span><span class="hl-2"> }</span><br/><span class="hl-2">);</span>
</code><button>Copy</button></pre>
<a id="md:performing-overload-resolution" class="tsd-anchor"></a><h3><a href="#md:performing-overload-resolution">Performing overload resolution</a></h3><pre><code class="language-typescript"><span class="hl-1">import</span><span class="hl-2"> { </span><span class="hl-4">forContract</span><span class="hl-2"> } </span><span class="hl-1">from</span><span class="hl-2"> </span><span class="hl-5">&quot;@truffle/encoder&quot;</span><span class="hl-2">;</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract1</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract1&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">contract2</span><span class="hl-2"> = </span><span class="hl-4">artifacts</span><span class="hl-2">.</span><span class="hl-6">require</span><span class="hl-2">(</span><span class="hl-5">&quot;Contract2&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">encoder</span><span class="hl-2"> = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">Encoder</span><span class="hl-2">.</span><span class="hl-6">forContract</span><span class="hl-2">(</span><span class="hl-4">Contract1</span><span class="hl-2">, [</span><span class="hl-4">Contract1</span><span class="hl-2">, </span><span class="hl-4">Contract2</span><span class="hl-2">]);</span><br/><span class="hl-3">const</span><span class="hl-2"> </span><span class="hl-10">abis</span><span class="hl-2"> = </span><span class="hl-4">Contract1</span><span class="hl-2">.</span><span class="hl-4">abi</span><span class="hl-2">.</span><span class="hl-6">filter</span><span class="hl-2">(</span><span class="hl-4">abiEntry</span><span class="hl-2"> </span><span class="hl-3">=&gt;</span><span class="hl-2"> </span><span class="hl-4">abiEntry</span><span class="hl-2">.</span><span class="hl-4">name</span><span class="hl-2"> === </span><span class="hl-5">&quot;overloaded&quot;</span><span class="hl-2">);</span><br/><span class="hl-3">const</span><span class="hl-2"> { </span><span class="hl-10">tx</span><span class="hl-2">, </span><span class="hl-10">abi</span><span class="hl-2"> } = </span><span class="hl-1">await</span><span class="hl-2"> </span><span class="hl-4">encoder</span><span class="hl-2">.</span><span class="hl-6">encodeTransaction</span><span class="hl-2">(</span><br/><span class="hl-2"> </span><span class="hl-4">abis</span><span class="hl-2">,</span><br/><span class="hl-2"> [</span><span class="hl-5">&quot;hello&quot;</span><span class="hl-2">, { </span><span class="hl-4">value:</span><span class="hl-2"> </span><span class="hl-7">1</span><span class="hl-2"> }],</span><br/><span class="hl-2"> { </span><span class="hl-4">allowOptions:</span><span class="hl-2"> </span><span class="hl-3">true</span><span class="hl-2"> }</span><br/><span class="hl-2">);</span>
</code><button>Copy</button></pre>
</div>
<div class="tsd-comment tsd-typography"></div></section><aside class="tsd-sources">
<ul>
<li>Defined in <a href="https://github.com/trufflesuite/truffle/blob/8c81e30a6/packages/encoder/lib/index.ts#L1">encoder/lib/index.ts:1</a></li></ul></aside>
<section class="tsd-panel-group tsd-index-group">
<section class="tsd-panel tsd-index-panel">
<h3 class="tsd-index-heading uppercase">Index</h3>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Classes - Encoder</h3>
<div class="tsd-index-list"><a href="../classes/_truffle_encoder.ContractEncoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-128"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-class)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M11.898 16.1201C11.098 16.1201 10.466 15.8961 10.002 15.4481C9.53803 15.0001 9.30603 14.3841 9.30603 13.6001V9.64012C9.30603 8.85612 9.53803 8.24012 10.002 7.79212C10.466 7.34412 11.098 7.12012 11.898 7.12012C12.682 7.12012 13.306 7.34812 13.77 7.80412C14.234 8.25212 14.466 8.86412 14.466 9.64012H13.386C13.386 9.14412 13.254 8.76412 12.99 8.50012C12.734 8.22812 12.37 8.09212 11.898 8.09212C11.426 8.09212 11.054 8.22412 10.782 8.48812C10.518 8.75212 10.386 9.13212 10.386 9.62812V13.6001C10.386 14.0961 10.518 14.4801 10.782 14.7521C11.054 15.0161 11.426 15.1481 11.898 15.1481C12.37 15.1481 12.734 15.0161 12.99 14.7521C13.254 14.4801 13.386 14.0961 13.386 13.6001H14.466C14.466 14.3761 14.234 14.9921 13.77 15.4481C13.306 15.8961 12.682 16.1201 11.898 16.1201Z" fill="var(--color-text)"></path></g></svg><span>Contract<wbr/>Encoder</span></a>
<a href="../classes/_truffle_encoder.ContractInstanceEncoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Instance<wbr/>Encoder</span></a>
<a href="../classes/_truffle_encoder.ProjectEncoder.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Project<wbr/>Encoder</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Classes - Exception</h3>
<div class="tsd-index-list"><a href="../classes/_truffle_encoder.ContractNotDeployedError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Deployed<wbr/>Error</span></a>
<a href="../classes/_truffle_encoder.ContractNotFoundError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Found<wbr/>Error</span></a>
<a href="../classes/_truffle_encoder.InvalidAddressError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Address<wbr/>Error</span></a>
<a href="../classes/_truffle_encoder.NoFunctionByThatNameError.html" class="tsd-index-link tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Function<wbr/>By<wbr/>That<wbr/>Name<wbr/>Error</span></a>
<a href="../classes/_truffle_encoder.NoInternalInfoError.html" class="tsd-index-link tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Internal<wbr/>Info<wbr/>Error</span></a>
<a href="../classes/_truffle_encoder.UnlinkedContractError.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unlinked<wbr/>Contract<wbr/>Error</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Interfaces</h3>
<div class="tsd-index-list"><a href="../interfaces/_truffle_encoder.EncoderSettings.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-256"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-interface)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.51 16V15.016H11.298V8.224H9.51V7.24H14.19V8.224H12.402V15.016H14.19V16H9.51Z" fill="var(--color-text)"></path></g></svg><span>Encoder<wbr/>Settings</span></a>
<a href="../interfaces/_truffle_encoder.EnsSettings.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Ens<wbr/>Settings</span></a>
<a href="../interfaces/_truffle_encoder.ResolveOptions.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Resolve<wbr/>Options</span></a>
<a href="../interfaces/_truffle_encoder.TxAndAbi.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Tx<wbr/>And<wbr/>Abi</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Functions - Constructors</h3>
<div class="tsd-index-list"><a href="../functions/_truffle_encoder.forArtifact.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-64"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-function)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.39 16V7.24H14.55V8.224H10.446V11.128H14.238V12.112H10.47V16H9.39Z" fill="var(--color-text)"></path></g></svg><span>for<wbr/>Artifact</span></a>
<a href="../functions/_truffle_encoder.forDeployedArtifact.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Artifact</span></a>
<a href="../functions/_truffle_encoder.forProject.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project</span></a>
<a href="../functions/_truffle_encoder.forProjectInternal.html" class="tsd-index-link tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project<wbr/>Internal</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Functions - Provider-based Constructor</h3>
<div class="tsd-index-list"><a href="../functions/_truffle_encoder.forArtifactAt.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact<wbr/>At</span></a>
</div></section>
<section class="tsd-index-section">
<h3 class="tsd-index-heading">Functions - Truffle Contract-based Constructors</h3>
<div class="tsd-index-list"><a href="../functions/_truffle_encoder.forContract.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract</span></a>
<a href="../functions/_truffle_encoder.forContractAt.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>At</span></a>
<a href="../functions/_truffle_encoder.forContractInstance.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>Instance</span></a>
<a href="../functions/_truffle_encoder.forDeployedContract.html" class="tsd-index-link"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Contract</span></a>
</div></section></section></section></div>
<div class="col-sidebar">
<div class="page-menu">
<div class="tsd-navigation settings">
<details class="tsd-index-accordion"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><path d="M4.93896 8.531L12 15.591L19.061 8.531L16.939 6.409L12 11.349L7.06098 6.409L4.93896 8.531Z" fill="var(--color-text)" id="icon-chevronDown"></path></svg>Settings</h3></summary>
<div class="tsd-accordion-details">
<div class="tsd-filter-visibility">
<h4 class="uppercase">Member Visibility</h4><form>
<ul id="tsd-filter-options">
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-protected" name="protected"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Protected</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-private" name="private"/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Private</span></label></li>
<li class="tsd-filter-item"><label class="tsd-filter-input"><input type="checkbox" id="tsd-filter-inherited" name="inherited" checked/><svg width="32" height="32" viewBox="0 0 32 32" aria-hidden="true"><rect class="tsd-checkbox-background" width="30" height="30" x="1" y="1" rx="6" fill="none"></rect><path class="tsd-checkbox-checkmark" d="M8.35422 16.8214L13.2143 21.75L24.6458 10.25" stroke="none" stroke-width="3.5" stroke-linejoin="round" fill="none"></path></svg><span>Inherited</span></label></li></ul></form></div>
<div class="tsd-theme-toggle">
<h4 class="uppercase">Theme</h4><select id="tsd-theme"><option value="os">OS</option><option value="light">Light</option><option value="dark">Dark</option></select></div></div></details></div>
<details open class="tsd-index-accordion tsd-page-navigation"><summary class="tsd-accordion-summary">
<h3><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg>On This Page</h3></summary>
<div class="tsd-accordion-details">
<ul>
<li>
<ul>
<li><a href="#md:truffle-encoder"><span>Truffle <wbr/>Encoder</span></a></li>
<li>
<ul>
<li><a href="#md:usage"><span>Usage</span></a></li>
<li>
<ul>
<li><a href="#md:initialization"><span>Initialization</span></a></li>
<li><a href="#md:encoder-methods"><span>Encoder methods</span></a></li>
<li><a href="#md:wrapped-format-information"><span>Wrapped format information</span></a></li>
<li><a href="#md:use-of-project-information-and-encoding-of-enums"><span>Use of project information and encoding of enums</span></a></li>
<li><a href="#md:ens-resolution"><span>ENS resolution</span></a></li>
<li><a href="#md:basic-usage-examples"><span>Basic usage examples</span></a></li>
<li>
<ul>
<li><a href="#md:encoding-a-transaction"><span>Encoding a transaction</span></a></li></ul></li>
<li><a href="#md:performing-overload-resolution"><span>Performing overload resolution</span></a></li></ul></li></ul></li></ul></li></ul></div></details></div>
<div class="site-menu">
<nav class="tsd-navigation"><a href="../index.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><g id="icon-4"><rect fill="var(--color-icon-background)" stroke="var(--color-ts-namespace)" stroke-width="1.5" x="1" y="1" width="22" height="22" rx="6"></rect><path d="M9.33 16V7.24H10.77L13.446 14.74C13.43 14.54 13.41 14.296 13.386 14.008C13.37 13.712 13.354 13.404 13.338 13.084C13.33 12.756 13.326 12.448 13.326 12.16V7.24H14.37V16H12.93L10.266 8.5C10.282 8.692 10.298 8.936 10.314 9.232C10.33 9.52 10.342 9.828 10.35 10.156C10.366 10.476 10.374 10.784 10.374 11.08V16H9.33Z" fill="var(--color-text)"></path></g></svg><span>Truffle <wbr/>Decoding and <wbr/>Encoding</span></a>
<ul class="tsd-small-nested-navigation">
<li><a href="_truffle_codec.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/codec</span></a></li>
<li>
<details class="tsd-index-accordion" open data-key="@truffle/encoder"><summary class="tsd-accordion-summary"><svg width="20" height="20" viewBox="0 0 24 24" fill="none"><use href="#icon-chevronDown"></use></svg><a href="_truffle_encoder.html" class="current"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>@truffle/encoder</span></a></summary>
<div class="tsd-accordion-details">
<ul class="tsd-nested-navigation">
<li><a href="../classes/_truffle_encoder.ContractEncoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Encoder</span></a></li>
<li><a href="../classes/_truffle_encoder.ContractInstanceEncoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Instance<wbr/>Encoder</span></a></li>
<li><a href="../classes/_truffle_encoder.ContractNotDeployedError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Deployed<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_encoder.ContractNotFoundError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Contract<wbr/>Not<wbr/>Found<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_encoder.InvalidAddressError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Invalid<wbr/>Address<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_encoder.NoFunctionByThatNameError.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Function<wbr/>By<wbr/>That<wbr/>Name<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_encoder.NoInternalInfoError.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>No<wbr/>Internal<wbr/>Info<wbr/>Error</span></a></li>
<li><a href="../classes/_truffle_encoder.ProjectEncoder.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Project<wbr/>Encoder</span></a></li>
<li><a href="../classes/_truffle_encoder.UnlinkedContractError.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-128"></use></svg><span>Unlinked<wbr/>Contract<wbr/>Error</span></a></li>
<li><a href="../interfaces/_truffle_encoder.EncoderSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Encoder<wbr/>Settings</span></a></li>
<li><a href="../interfaces/_truffle_encoder.EnsSettings.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Ens<wbr/>Settings</span></a></li>
<li><a href="../interfaces/_truffle_encoder.ResolveOptions.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Resolve<wbr/>Options</span></a></li>
<li><a href="../interfaces/_truffle_encoder.TxAndAbi.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-256"></use></svg><span>Tx<wbr/>And<wbr/>Abi</span></a></li>
<li><a href="../functions/_truffle_encoder.forArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact</span></a></li>
<li><a href="../functions/_truffle_encoder.forArtifactAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Artifact<wbr/>At</span></a></li>
<li><a href="../functions/_truffle_encoder.forContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract</span></a></li>
<li><a href="../functions/_truffle_encoder.forContractAt.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>At</span></a></li>
<li><a href="../functions/_truffle_encoder.forContractInstance.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Contract<wbr/>Instance</span></a></li>
<li><a href="../functions/_truffle_encoder.forDeployedArtifact.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Artifact</span></a></li>
<li><a href="../functions/_truffle_encoder.forDeployedContract.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Deployed<wbr/>Contract</span></a></li>
<li><a href="../functions/_truffle_encoder.forProject.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project</span></a></li>
<li><a href="../functions/_truffle_encoder.forProjectInternal.html" class="tsd-is-protected"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-64"></use></svg><span>for<wbr/>Project<wbr/>Internal</span></a></li></ul></div></details></li>
<li><a href="decoder_lib.html"><svg class="tsd-kind-icon" viewBox="0 0 24 24"><use href="#icon-4"></use></svg><span>decoder/lib</span></a></li></ul></nav></div></div></div>
<div class="tsd-generator">
<p>Generated using <a href="https://typedoc.org/" target="_blank">TypeDoc</a></p></div>
<div class="overlay"></div></body></html>